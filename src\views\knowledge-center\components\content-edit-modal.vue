<script setup>
import { computed, onBeforeUnmount, ref, shallowRef, watch } from 'vue';
import { NButton, NForm, NFormItem, NInput, NInputNumber, NModal, NSelect, NUpload, useMessage } from 'naive-ui';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import '@wangeditor/editor/dist/css/style.css';
import { updateCourseContent } from '@/service/api/course';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

defineOptions({
  name: 'ContentEditModal'
});

const editorRef = shallowRef();
const html = ref('');
const toolbarConfig = {};
const editorConfig = { placeholder: '请输入内容...' };
const mode = ref('default');
const showModal = ref(false);
const previewImageUrl = ref('');

const activePreviewType = ref('cover');

const onCreated = editor => {
  editorRef.value = editor;
};

onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor) {
    editor.destroy();
  }
});

const model = ref({
  id: null,
  parent_id: null,
  title: '',
  content: '',
  view: 0,
  points: 0
});

// 用于比较的原始数据
const originalData = ref({
  id: null,
  parent_id: null,
  title: '',
  content: '',
  cover: '',
  medias: [],
  view: 0,
  points: 0
});

watch(
  () => html.value,
  newValue => {
    model.value.content = newValue;
  }
);

const props = defineProps({
  activeTab: {
    type: String,
    default: 'free'
  },
  firstLevelMenus: {
    type: Array,
    default: () => []
  },
  secondLevelMenusByMenu: {
    type: Object,
    default: () => ({})
  },
  rowData: {
    type: Object,
    default: () => ({})
  },
  visible: {
    type: Boolean,
    default: false
  },
  selectedContentFirstLevelMenu: {
    type: String,
    default: ''
  },
  selectedContentSecondLevelMenu: {
    type: Number,
    default: null
  }
});

const emit = defineEmits(['update:visible', 'refresh']);

const message = useMessage();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const loading = ref(false);
const selectedFirstLevelMenu = ref('');
const availableSecondLevelMenus = ref([]);

const coverFile = ref(null);
const fileList = ref([]);
const mediaFiles = ref([]);
const mediaFileList = ref([]);

watch(
  () => selectedFirstLevelMenu.value,
  newMenu => {
    if (newMenu && props.secondLevelMenusByMenu[newMenu]) {
      availableSecondLevelMenus.value = props.secondLevelMenusByMenu[newMenu].map(item => ({
        label: item.name,
        value: item.id
      }));
    } else {
      availableSecondLevelMenus.value = [];
    }
  }
);

watch(
  () => props.visible,
  newVisible => {
    if (newVisible && props.rowData) {
      initializeFormData(props.rowData);
    }
  }
);

// 是否显示积分字段
const showPointsField = computed(() => {
  return props.activeTab === 'paid';
});

function initializeFormData(data) {
  if (!data) return;

  // 保存原始数据用于比较
  originalData.value = {
    ...data,
    // 确保medias始终是数组
    medias: data.medias || [],
    view: data.view || 0,
    points: data.points || 0
  };

  // 设置表单数据
  model.value = {
    id: data.id,
    parent_id: data.parent_id || props.selectedContentSecondLevelMenu,
    title: data.title,
    content: data.content,
    view: data.view || 0,
    points: data.points || 0
  };

  html.value = data.content;

  // 如果没有parent_id，使用当前选中的二级菜单ID
  if (!data.parent_id && props.selectedContentSecondLevelMenu) {
    model.value.parent_id = props.selectedContentSecondLevelMenu;
  }

  // 设置一级菜单
  let parentMenu = null;

  // 尝试从数据中找到对应的一级菜单
  if (model.value.parent_id) {
    parentMenu = props.firstLevelMenus.find(menu => {
      return props.secondLevelMenusByMenu[menu]?.some(item => item.id === model.value.parent_id);
    });
  }

  // 如果找不到，使用当前选中的一级菜单
  if (!parentMenu && props.selectedContentFirstLevelMenu) {
    parentMenu = props.selectedContentFirstLevelMenu;
  }

  selectedFirstLevelMenu.value = parentMenu || '';

  // 设置封面图片
  if (data.cover) {
    fileList.value = [
      {
        id: Date.now().toString(),
        name: 'cover',
        status: 'finished',
        url: data.cover,
        source: 'cover'
      }
    ];
  } else {
    fileList.value = [];
  }

  // 设置媒体文件
  if (data.medias && Array.isArray(data.medias)) {
    mediaFileList.value = data.medias.map((media, index) => {
      // 处理新的媒体文件数据结构
      let url = '';
      let filename = `media_${index}`;

      if (typeof media === 'string') {
        // 处理旧格式：直接是URL字符串
        url = media;
      } else if (media && typeof media === 'object') {
        // 处理新格式：包含path、type和filename的对象
        url = media.path || '';
        filename = media.filename || `media_${index}`;
      }

      return {
        id: `${Date.now().toString()}_${index}`,
        name: filename,
        status: 'finished',
        url,
        source: 'media'
      };
    });
  } else {
    mediaFileList.value = [];
  }
}

const rules = {
  parent_id: defaultRequiredRule,
  title: defaultRequiredRule,
  content: defaultRequiredRule
};

const title = computed(() => {
  return props.activeTab === 'free' ? '编辑免费课程内容' : '编辑付费课程内容';
});

function resetForm() {
  selectedFirstLevelMenu.value = '';
  availableSecondLevelMenus.value = [];
  model.value = {
    id: null,
    parent_id: null,
    title: '',
    content: '',
    view: 0,
    points: 0
  };
  html.value = '';
  coverFile.value = null;
  fileList.value = [];
  mediaFiles.value = [];
  mediaFileList.value = [];
  restoreValidation();
}

async function handleSubmit() {
  try {
    await validate();
    loading.value = true;

    const formData = new FormData();
    formData.append('parent_id', String(model.value.parent_id));
    formData.append('title', model.value.title);
    formData.append('content', model.value.content);
    formData.append('view', String(model.value.view));

    // 如果是付费课程，添加积分字段
    if (props.activeTab === 'paid') {
      formData.append('points', String(model.value.points));
    }

    if (coverFile.value) {
      formData.append('cover', coverFile.value);
    }

    // 处理新上传的媒体文件
    mediaFiles.value.forEach(file => {
      if (file.file) {
        formData.append('medias[]', file.file);
      } else {
        formData.append('medias[]', file);
      }
    });

    // 处理已有的媒体文件
    const existingMedias = mediaFileList.value.filter(
      file => !mediaFiles.value.some(newFile => newFile.id === file.id)
    );

    if (existingMedias.length > 0) {
      formData.append('existing_medias', JSON.stringify(existingMedias.map(file => file.url)));
    }

    const response = await updateCourseContent(model.value.id, formData);

    if (response.response.data.code === 200) {
      message.success(response.response.data.message || '内容更新成功');
      emit('update:visible', false);
      emit('refresh');
    } else {
      message.error(response.response.data.message || '更新失败');
    }
  } catch {
    message.error('提交表单时发生错误');
  } finally {
    loading.value = false;
  }
}

function closeModal() {
  emit('update:visible', false);
  resetForm();
}

function handlePreview(file) {
  if (file && file.url) {
    previewImageUrl.value = file.url;
    activePreviewType.value = file.source === 'media' ? 'media' : 'cover';
    showModal.value = true;
  }
}

function handleCoverUpload(options) {
  if (options && options.file) {
    const actualFile = options.file.file || options.file;
    coverFile.value = actualFile;

    const reader = new FileReader();
    reader.onload = e => {
      const url = typeof e.target.result === 'string' ? e.target.result : '';

      fileList.value = [
        {
          id: Date.now().toString(),
          name: actualFile.name,
          status: 'finished',
          url,
          file: actualFile,
          source: 'cover'
        }
      ];
    };
    reader.readAsDataURL(actualFile);
  }
}

function handleMediaUpload(options) {
  if (options && options.fileList && options.fileList.length > 0) {
    // 过滤出新添加的文件，避免重复
    const newFiles = options.fileList
      .filter(fileInfo => {
        const fileName = (fileInfo.file || fileInfo).name;
        // 检查是否已经存在于mediaFiles中
        return !mediaFiles.value.some(existing => {
          const existingName = existing.file ? existing.file.name : existing.name;
          return existingName === fileName;
        });
      })
      .map(fileInfo => fileInfo.file || fileInfo);

    if (newFiles.length === 0) return; // 如果没有新文件，直接返回

    // 添加新文件到媒体文件列表
    mediaFiles.value = [...mediaFiles.value, ...newFiles];

    // 为新文件创建显示对象
    const newMediaFileList = newFiles.map(file => {
      const actualFile = file.file || file;
      const fileObj = {
        id: `${Date.now().toString()}_${Math.random().toString(36).substr(2, 9)}`,
        name: actualFile.name,
        status: 'finished',
        file: actualFile,
        source: 'media'
      };

      if (actualFile.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = e => {
          fileObj.url = typeof e.target.result === 'string' ? e.target.result : '';
        };
        reader.readAsDataURL(actualFile);
      }

      return fileObj;
    });

    // 只添加新的文件到显示列表
    mediaFileList.value = [...mediaFileList.value, ...newMediaFileList];
  }
}

function removeCoverImage() {
  coverFile.value = null;
  fileList.value = [];
}

function handleMediaRemove(options) {
  const { file } = options;

  // 从媒体文件列表中移除
  const fileIndex = mediaFileList.value.findIndex(item => item.id === file.id);
  if (fileIndex !== -1) {
    mediaFileList.value.splice(fileIndex, 1);
  }

  // 从新上传的文件列表中移除
  const newFileIndex = mediaFiles.value.findIndex(item => {
    if (item.id) {
      return item.id === file.id;
    }
    return item.name === file.name;
  });

  if (newFileIndex !== -1) {
    mediaFiles.value.splice(newFileIndex, 1);
  }
}
</script>

<template>
  <NModal
    :show="props.visible"
    preset="card"
    :title="title"
    :bordered="false"
    style="width: 70vw"
    size="medium"
    :segmented="{
      content: 'soft',
      footer: 'soft'
    }"
    :mask-closable="false"
    @update:show="value => emit('update:visible', value)"
  >
    <div class="modal-content">
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" label-width="100px">
        <NFormItem label="一级菜单">
          <NSelect
            v-model:value="selectedFirstLevelMenu"
            :options="firstLevelMenus.map(menu => ({ label: menu, value: menu }))"
            placeholder="请选择一级菜单"
            disabled
          />
        </NFormItem>

        <NFormItem label="二级菜单" path="parent_id">
          <NSelect
            v-model:value="model.parent_id"
            :options="availableSecondLevelMenus"
            placeholder="请选择二级菜单"
            disabled
          />
        </NFormItem>

        <NFormItem label="标题" path="title">
          <NInput v-model:value="model.title" placeholder="请输入内容标题" />
        </NFormItem>

        <NFormItem label="浏览量" path="view">
          <NInputNumber v-model:value="model.view" :min="0" placeholder="请输入浏览量" />
        </NFormItem>

        <NFormItem v-if="showPointsField" label="所需积分" path="points">
          <NInputNumber v-model:value="model.points" :min="0" placeholder="请输入所需积分" />
        </NFormItem>

        <NFormItem label="内容" path="content">
          <div class="editor-container">
            <Toolbar
              style="border-bottom: 1px solid #ccc"
              :editor="editorRef"
              :default-config="toolbarConfig"
              :mode="mode"
            />
            <Editor
              v-model="html"
              style="height: 320px; overflow-y: hidden"
              :default-config="editorConfig"
              :mode="mode"
              @on-created="onCreated"
            />
          </div>
        </NFormItem>

        <NFormItem label="封面图片">
          <div class="flex flex-col gap-2">
            <template v-if="fileList.length === 0">
              <NUpload
                accept="image/*"
                list-type="image-card"
                :default-upload="false"
                :file-list="fileList"
                @change="handleCoverUpload"
                @preview="handlePreview"
              />
            </template>
            <template v-else>
              <div class="flex items-center gap-4">
                <div class="image-preview" @click="handlePreview(fileList[0])">
                  <img
                    :src="fileList[0].url"
                    alt="封面图片"
                    style="width: 104px; height: 104px; object-fit: cover; cursor: pointer"
                  />
                </div>
                <NButton size="small" type="error" @click="removeCoverImage">更换封面</NButton>
              </div>
            </template>
          </div>
        </NFormItem>

        <NFormItem label="媒体文件">
          <NUpload
            accept="image/*,video/*"
            multiple
            list-type="image-card"
            :default-upload="false"
            :file-list="mediaFileList"
            @change="handleMediaUpload"
            @preview="handlePreview"
            @remove="handleMediaRemove"
          />
        </NFormItem>
      </NForm>
    </div>

    <template #footer>
      <div class="flex justify-end gap-12px">
        <NButton @click="closeModal">取消</NButton>
        <NButton type="primary" :loading="loading" @click="handleSubmit">保存</NButton>
      </div>
    </template>
  </NModal>

  <NModal
    v-model:show="showModal"
    preset="card"
    style="width: 600px"
    :title="activePreviewType === 'media' ? '预览媒体文件' : '预览封面图片'"
  >
    <img :src="previewImageUrl" style="width: 100%" />
  </NModal>
</template>

<style scoped>
.modal-content {
  max-height: calc(70vh - 120px);
  overflow-y: auto;
  padding-right: 8px;
}

.editor-container {
  border: 1px solid #ccc;
  z-index: 100;
}
</style>
