import { request } from '../request';

/** Get model list */
export function fetchModelList(params?: { page?: number; pageSize?: number }) {
  return request<{
    list: Api.Model.ModelItem[];
    global_config: string;
    pagination?: {
      total: number;
      currentPage: number;
      pageSize: number;
      totalPages: number;
    };
  }>({
    url: '/api/admin/model/list',
    method: 'get',
    params
  });
}

/** Create global model configuration */
export function createGlobalModelConfig(data: {
  aiteam_baseurl: string;
  aiteam_apikey: string;
  aiteam_points: string;
}) {
  return request<{ message: string }>({
    url: '/api/admin/model/config/create',
    method: 'post',
    data
  });
}

/** Add model configuration */
export function addModelConfig(data: FormData) {
  return request<{ message: string }>({
    url: '/api/admin/model/config/add',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  });
}

/** Update model configuration */
export function updateModelConfig(data: FormData) {
  return request<{ message: string }>({
    url: '/api/admin/model/config/update',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  });
}

/** Delete model configuration */
export function deleteModelConfig(id: number) {
  return request<{ message: string }>({
    url: '/api/admin/model/config/delete',
    method: 'delete',
    params: { id }
  });
}
