<script setup lang="ts">
import { h, ref } from 'vue';
import { NButton, NCard, NDataTable, NModal, NTag } from 'naive-ui';
import type { DataTableProps } from 'naive-ui';
import { fetchCreationRecordList } from '@/service/api/creation';
import { useAppStore } from '@/store/modules/app';
import { useTable } from '@/hooks/common/table';

defineOptions({ name: 'CreationRecord' });

const appStore = useAppStore();

const showModal = ref(false);
const videoUrl = ref('');

const typeMap: Record<string, { text: string; color: string }> = {
  text2video: { text: '文生视频', color: 'info' },
  image2video: { text: '图生视频', color: 'primary' },
  reference2video: { text: '参考生视频', color: 'success' }
};

const stateMap: Record<string, { text: string; color: string }> = {
  created: { text: '创建成功', color: 'success' },
  queueing: { text: '排队中', color: 'warning' },
  processing: { text: '处理中', color: 'info' },
  success: { text: '处理成功', color: 'success' },
  failed: { text: '失败', color: 'error' }
};

const { columns, data, getDataByPage, loading, pagination } = useTable<Api.CreationRecord.RecordItem>({
  apiFn: fetchCreationRecordList,
  showTotal: true,
  dataField: 'list',
  paginationField: 'pagination',
  totalField: 'total',
  pageField: 'currentPage',
  pageSizeField: 'pageSize',
  apiParams: {
    page: 1,
    pageSize: 10
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      align: 'center',
      width: 80
    },
    {
      key: 'member_id',
      title: '用户ID',
      align: 'center',
      width: 100
    },
    {
      key: 'type',
      title: '制作模式',
      align: 'center',
      width: 120,
      render: row => {
        const typeInfo = typeMap[row.type] || { text: row.type, color: 'default' };
        return h(
          NTag,
          { type: typeInfo.color as DataTableProps['tagProps']['type'], round: true },
          { default: () => typeInfo.text }
        );
      }
    },
    {
      key: 'state',
      title: '制作状态',
      align: 'center',
      width: 100,
      render: row => {
        const stateInfo = stateMap[row.state] || { text: row.state, color: 'default' };
        return h(
          NTag,
          { type: stateInfo.color as DataTableProps['tagProps']['type'], round: true },
          { default: () => stateInfo.text }
        );
      }
    },
    {
      key: 'points_used',
      title: '花费积分',
      align: 'center',
      width: 100
    },
    {
      key: 'prompt',
      title: 'Prompt',
      align: 'left',
      minWidth: 200,
      ellipsis: {
        tooltip: true
      }
    },
    {
      key: 'creations',
      title: '生成内容',
      align: 'center',
      width: 120,
      render: row => {
        if (row.creations && row.creations.length > 0 && row.creations[0].url) {
          return h(
            NButton,
            {
              text: true,
              tag: 'a',
              type: 'primary',
              onClick: () => {
                videoUrl.value = row.creations[0].url;
                showModal.value = true;
              }
            },
            { default: () => '查看' }
          );
        }
        return '无';
      }
    },
    {
      key: 'updated_at',
      title: '制作时间',
      align: 'center',
      width: 180,
      render: row => {
        return new Date(row.updated_at).toLocaleString();
      }
    }
  ]
});

// Initial data fetch
getDataByPage();
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <NCard title="制作记录" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="1200"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="{
          ...pagination,
          onChange: (page: number) => pagination.onUpdatePage && pagination.onUpdatePage(page),
          onUpdatePageSize: (pageSize: number) => pagination.onUpdatePageSize && pagination.onUpdatePageSize(pageSize)
        }"
        class="sm:h-full"
      />
    </NCard>
    <NModal
      v-model:show="showModal"
      preset="card"
      style="width: 90%; max-width: 800px"
      title="查看视频"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <video
        v-if="videoUrl"
        :src="videoUrl"
        controls
        autoplay
        style="width: 100%; max-height: 70vh; object-fit: cover"
      ></video>
      <div v-else>没有可播放的视频</div>
    </NModal>
  </div>
</template>

<style scoped></style>
