<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import {
  <PERSON><PERSON><PERSON>on,
  NDrawer,
  NDrawerContent,
  NForm,
  NFormItem,
  NInput,
  NRadio,
  NRadioGroup,
  NSelect,
  useMessage
} from 'naive-ui';
import { createPolicy } from '@/service/api/policy';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

defineOptions({
  name: 'MenuDrawer'
});

interface Props {
  /** Current active tab */
  activeTab: 'platform' | 'entre';
  /** Existing first-level menus */
  existingMenus: string[];
}

const props = defineProps<Props>();

interface Emits {
  (e: 'refresh'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const message = useMessage();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const menuType = ref<'existing' | 'new'>('existing');
const loading = ref(false);

const model = ref({
  level_one: '',
  level_two: '',
  title: props.activeTab
});

// Update title when activeTab changes
watch(
  () => props.activeTab,
  newTab => {
    model.value.title = newTab;
  }
);

const rules = {
  level_one: defaultRequiredRule,
  level_two: defaultRequiredRule
};

const title = computed(() => {
  return props.activeTab === 'platform' ? '添加平台政策菜单' : '添加创业政策菜单';
});

function resetForm() {
  menuType.value = 'existing';
  model.value = {
    level_one: '',
    level_two: '',
    title: props.activeTab
  };
  restoreValidation();
}

async function handleSubmit() {
  try {
    await validate();
    loading.value = true;

    const response = await createPolicy(model.value);

    if (response.response.data.code === 200) {
      message.success(response.response.data.message || '添加成功');
      visible.value = false;
      emit('refresh');
    }
  } catch (error) {
    message.error('提交表单时发生错误');
  } finally {
    loading.value = false;
  }
}

function closeDrawer() {
  visible.value = false;
  resetForm();
}

// Watch for drawer visibility changes to reset form
watch(visible, newVisible => {
  if (!newVisible) {
    resetForm();
  }
});
</script>

<template>
  <div class="menu-drawer-wrapper">
    <NDrawer v-model:show="visible" display-directive="show" :width="360">
      <NDrawerContent :title="title" :native-scrollbar="false" closable @close="closeDrawer">
        <NForm ref="formRef" :model="model" :rules="rules">
          <NFormItem label="菜单类型">
            <NRadioGroup v-model:value="menuType">
              <NRadio value="existing">选择已有一级菜单</NRadio>
              <NRadio value="new">创建新的一级菜单</NRadio>
            </NRadioGroup>
          </NFormItem>

          <NFormItem label="一级菜单" path="level_one">
            <NSelect
              v-if="menuType === 'existing' && existingMenus.length > 0"
              v-model:value="model.level_one"
              :options="existingMenus.map(menu => ({ label: menu, value: menu }))"
              placeholder="请选择一级菜单"
            />
            <NInput v-else v-model:value="model.level_one" placeholder="请输入一级菜单名称" />
          </NFormItem>

          <NFormItem label="二级菜单" path="level_two">
            <NInput v-model:value="model.level_two" placeholder="请输入二级菜单名称（使用'-'一键添加）" />
          </NFormItem>
        </NForm>

        <template #footer>
          <div class="flex justify-end gap-12px">
            <NButton @click="closeDrawer">取消</NButton>
            <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
          </div>
        </template>
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style scoped>
</style>
