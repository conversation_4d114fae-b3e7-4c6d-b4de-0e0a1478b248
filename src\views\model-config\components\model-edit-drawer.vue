<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import {
  NButton,
  NDrawer,
  NDrawerContent,
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NModal,
  NSlider,
  NUpload,
  useMessage
} from 'naive-ui';

defineOptions({ name: 'ModelEditDrawer' });

interface Props {
  visible: boolean;
  model: Partial<Api.Model.ModelItem> | null;
  isGlobal: boolean; // 保留此属性以保持与父组件的兼容性
}

const props = defineProps<Props>();

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'close'): void;
  (e: 'save', model: Partial<Api.Model.ModelItem>): void;
}

const emit = defineEmits<Emits>();

const message = useMessage();
const formRef = ref<typeof NForm | null>(null);
const formModel = ref<Partial<Api.Model.ModelItem>>({
  name: '',
  model_id: '',
  points: 0,
  prompt: '',
  temperature: 0.7,
  baseurl: '',
  apikey: ''
});

// 头像上传相关
const avatarFile = ref<File | null>(null);
const avatarFileList = ref<any[]>([]);
const showPreviewModal = ref(false);
const previewImageUrl = ref('');

// Form validation rules
const rules = {
  name: {
    required: true,
    message: '请输入模型名称',
    trigger: 'blur'
  },
  model_id: {
    required: true,
    message: '请输入模型ID',
    trigger: 'blur'
  },
  points: {
    required: true,
    type: 'number',
    message: '请输入消耗积分',
    trigger: 'blur'
  },
  temperature: {
    required: true,
    type: 'number',
    message: '请输入温度控制值(0.01-1.00)',
    trigger: 'blur',
    validator: (_rule, value) => {
      return value >= 0.01 && value <= 1.0;
    }
  }
};

// Computed properties
const title = computed(() => {
  if (!props.model) return '添加模型';
  return props.model.id ? '编辑模型' : '添加模型';
});

// Watch for model changes
watch(
  () => props.model,
  newModel => {
    if (newModel) {
      formModel.value = { ...newModel };

      if (newModel.avatar) {
        avatarFileList.value = [
          {
            id: Date.now().toString(),
            name: '当前头像',
            status: 'finished',
            url: newModel.avatar,
            file: null
          }
        ];
      } else {
        avatarFileList.value = [];
      }
    } else {
      resetForm();
    }
  },
  { immediate: true }
);

watch(
  () => props.visible,
  newVisible => {
    if (!newVisible) {
      resetForm();
    }
  }
);

// 处理头像上传
function handleAvatarUpload(options) {
  if (options && options.file) {
    const actualFile = options.file.file || options.file;
    avatarFile.value = actualFile;

    const reader = new FileReader();
    reader.onload = e => {
      const url = typeof e.target.result === 'string' ? e.target.result : '';

      avatarFileList.value = [
        {
          id: Date.now().toString(),
          name: actualFile.name,
          status: 'finished',
          url,
          file: actualFile
        }
      ];
    };
    reader.readAsDataURL(actualFile);
  }
}

// 移除头像
function removeAvatar() {
  avatarFile.value = null;
  avatarFileList.value = [];
}

// 预览头像
function handlePreview(file) {
  if (file && file.url) {
    previewImageUrl.value = file.url;
    showPreviewModal.value = true;
  }
}

// Reset form
function resetForm() {
  formModel.value = {
    name: '',
    model_id: '',
    points: 0,
    prompt: '',
    temperature: 0.7,
    baseurl: '',
    apikey: ''
  };
  avatarFile.value = null;
  avatarFileList.value = [];
}

// Close drawer
function closeDrawer() {
  emit('update:visible', false);
  emit('close');
}

// Submit form
async function handleSubmit() {
  const valid = await formRef.value
    ?.validate()
    .then(() => true)
    .catch(() => false);

  if (valid) {
    const formData = new FormData();

    if (formModel.value.id) {
      formData.append('id', String(formModel.value.id));
    }
    formData.append('name', formModel.value.name || '');
    formData.append('model_id', formModel.value.model_id || '');
    formData.append('points', String(formModel.value.points || 0));
    formData.append('prompt', formModel.value.prompt || '');
    formData.append('temperature', String(formModel.value.temperature || 0.7));

    // 无论是否为全局模型，都添加baseurl和apikey字段
    formData.append('baseurl', formModel.value.baseurl || '');
    formData.append('apikey', formModel.value.apikey || '');

    if (avatarFile.value) {
      formData.append('avatar', avatarFile.value);
    } else if (avatarFileList.value.length > 0 && avatarFileList.value[0].url) {
      formData.append('avatar_url', avatarFileList.value[0].url);
    }

    emit('save', formData);
  }
}
</script>

<template>
  <NDrawer :show="visible" :width="600" placement="right" @update:show="val => emit('update:visible', val)">
    <NDrawerContent :title="title" closable @close="closeDrawer">
      <NForm
        ref="formRef"
        :model="formModel"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        class="model-form"
      >
        <NFormItem label="头像" path="avatar">
          <div class="avatar-upload-container">
            <template v-if="avatarFileList.length === 0">
              <NUpload
                accept="image/*"
                list-type="image-card"
                :default-upload="false"
                :file-list="avatarFileList"
                @change="handleAvatarUpload"
                @preview="handlePreview"
              />
            </template>
            <template v-else>
              <div class="flex items-center gap-4">
                <div class="avatar-preview" @click="handlePreview(avatarFileList[0])">
                  <img :src="avatarFileList[0].url" alt="模型头像" class="avatar-image" />
                </div>
                <NButton size="small" type="error" @click="removeAvatar">更换头像</NButton>
              </div>
            </template>
          </div>
        </NFormItem>

        <NFormItem label="模型名称" path="name">
          <NInput v-model:value="formModel.name" placeholder="请输入模型名称" />
        </NFormItem>

        <NFormItem label="模型ID" path="model_id">
          <NInput v-model:value="formModel.model_id" placeholder="请输入模型ID" />
        </NFormItem>

        <NFormItem label="消耗积分" path="points">
          <NInputNumber v-model:value="formModel.points" placeholder="请输入消耗积分" :min="0" />
        </NFormItem>

        <NFormItem label="系统提示词" path="prompt">
          <NInput
            v-model:value="formModel.prompt"
            type="textarea"
            placeholder="请输入系统提示词"
            :autosize="{ minRows: 3, maxRows: 6 }"
          />
        </NFormItem>

        <NFormItem label="温度控制" path="temperature">
          <div class="temperature-control-container">
            <NSlider
              v-model:value="formModel.temperature"
              :min="0.01"
              :max="1.0"
              :step="0.01"
              class="temperature-slider"
            />
            <NInputNumber
              v-model:value="formModel.temperature"
              :min="0.01"
              :max="1.0"
              :step="0.01"
              class="input-width-80"
            />
          </div>
        </NFormItem>

        <!-- BaseUrl和API Key字段，对所有模型都显示 -->
        <NFormItem label="Base URL" path="baseurl">
          <NInput v-model:value="formModel.baseurl" placeholder="请输入Base URL" />
        </NFormItem>

        <NFormItem label="API Key" path="apikey">
          <NInput
            v-model:value="formModel.apikey"
            type="password"
            show-password-on="click"
            placeholder="请输入API Key"
          />
        </NFormItem>
      </NForm>

      <template #footer>
        <div class="flex justify-end gap-2">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" @click="handleSubmit">保存</NButton>
        </div>
      </template>
    </NDrawerContent>
  </NDrawer>

  <!-- 预览模态框 -->
  <NModal v-model:show="showPreviewModal" preset="card" class="preview-modal" title="预览头像">
    <img :src="previewImageUrl" class="preview-image" />
  </NModal>
</template>

<style scoped>
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-end {
  justify-content: flex-end;
}

.gap-2 {
  gap: 8px;
}

.gap-4 {
  gap: 16px;
}

.temperature-control-container {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.temperature-slider {
  flex: 1;
  min-width: 75%;
}

/* 确保滑块在Naive UI中正确显示 */
:deep(.n-slider) {
  width: 100%;
  max-width: 100%;
}

:deep(.n-slider-rail) {
  width: 100%;
}

.input-width-80 {
  width: 80px;
}

/* 确保表单项有足够的宽度 */
.model-form {
  width: 100%;
}

.model-form :deep(.n-form-item-feedback-wrapper) {
  min-height: 24px;
}

/* 确保所有输入控件都有足够的宽度 */
.model-form :deep(.n-input),
.model-form :deep(.n-input-number) {
  width: 100%;
}

/* 头像上传相关样式 */
.avatar-upload-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.avatar-preview {
  width: 104px;
  height: 104px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 预览模态框样式 */
.preview-modal {
  width: 600px;
}

.preview-image {
  width: 100%;
}
</style>
