<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { fetchConstantData } from '@/service/api/home';
import { useAppStore } from '@/store/modules/app';
import CardData from './modules/card-data.vue';
import LineChart from './modules/line-chart.vue';
import PieChart from './modules/pie-chart.vue';

const appStore = useAppStore();
const gap = computed(() => (appStore.isMobile ? 0 : 16));

// Data from API
const apiData = ref({
  register_time: '',
  visits: 0,
  member_count: 0,
  daily_visits: {},
  member_makes_total: 0,
  member_makes_by_type: {
    text2video: 0,
    image2video: 0,
    reference2video: 0,
    text2image: 0
  }
});

// Watch for changes in apiData and log them
watch(apiData, _newValue => {}, { deep: true });

const visitCount = computed(() => Number(apiData.value.visits || 0));
const memberCount = computed(() => apiData.value.member_count || 0);
const makeCount = computed(() => apiData.value.member_makes_total || 0);
const makesByType = computed(
  () =>
    apiData.value.member_makes_by_type || {
      text2video: 0,
      image2video: 0,
      reference2video: 0,
      text2image: 0
    }
);
const runDays = computed(() => {
  if (!apiData.value.register_time) return 0;
  const registerTime = Number(apiData.value.register_time);
  const now = Date.now();
  return Math.floor((now - registerTime) / (1000 * 60 * 60 * 24));
});

const dailyVisits = computed(() => {
  if (!apiData.value.daily_visits || Object.keys(apiData.value.daily_visits).length === 0)
    return { dates: [], values: [] };

  const entries = Object.entries(apiData.value.daily_visits);
  return {
    dates: entries.map(([date]) => {
      const [_, month, day] = date.split('-');
      return `${month}-${day}`;
    }),
    values: entries.map(([, count]) => count)
  };
});

async function fetchData() {
  try {
    const response = await fetchConstantData();

    let dataToAssign = null;
    if (response.data) {
      dataToAssign = response.data;
    }

    if (dataToAssign) {
      apiData.value = {
        ...apiData.value,
        ...dataToAssign
      };
    }
  } catch {
    // If handling the error specifically is not needed, the variable can be omitted.
  }
}

onMounted(() => {
  fetchData();
});
</script>

<template>
  <NSpace vertical :size="16">
    <CardData :visit-count="visitCount" :user-count="memberCount" :make-count="makeCount" :run-days="runDays" />
    <NGrid :x-gap="gap" :y-gap="16" responsive="screen" item-responsive>
      <NGi span="24 s:24 m:14">
        <NCard :bordered="false" class="card-wrapper">
          <LineChart :daily-visits="dailyVisits" />
        </NCard>
      </NGi>
      <NGi span="24 s:24 m:10">
        <NCard :bordered="false" class="card-wrapper">
          <PieChart :makes-by-type="makesByType" />
        </NCard>
      </NGi>
    </NGrid>
  </NSpace>
</template>

<style scoped></style>
