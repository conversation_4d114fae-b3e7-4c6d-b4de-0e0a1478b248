<script setup lang="ts">
import { h } from 'vue';
import { NAvatar, NButton, NPopconfirm } from 'naive-ui';
import { batchDeleteUsers, deleteUser, fetchUserList, updateUser } from '@/service/api/user';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import UserOperateDrawer from './modules/user-operate-drawer.vue';
import UserSearch from './modules/user-search.vue';

const appStore = useAppStore();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchUserList,
  showTotal: true,
  dataField: 'users',
  paginationField: 'pagination',
  totalField: 'total',
  pageField: 'currentPage',
  pageSizeField: 'pageSize',
  apiParams: {
    page: 1,
    pageSize: 10,
    nickname: null,
    mobile: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'avatar',
      title: $t('page.manage.user.avatar'),
      align: 'center',
      width: 80,
      render: row => {
        return h(NAvatar, {
          size: 'small',
          round: true,
          src: row.avatar || '',
          fallbackSrc: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
          objectFit: 'cover',
          alt: $t('page.manage.user.avatarOptions.placeholder'),
          title: row.nickname
        });
      }
    },
    {
      key: 'member_id',
      title: $t('page.manage.user.memberId'),
      align: 'center',
      width: 120
    },
    {
      key: 'nickname',
      title: $t('page.manage.user.nickname'),
      align: 'center',
      minWidth: 120
    },
    {
      key: 'mobile',
      title: $t('page.manage.user.mobile'),
      align: 'center',
      width: 150
    },
    {
      key: 'points',
      title: $t('page.manage.user.points'),
      align: 'center',
      width: 100
    },
    {
      key: 'materials',
      title: $t('page.manage.user.materials'),
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      key: 'vip_time',
      title: $t('page.manage.user.vipTime'),
      align: 'center',
      width: 180
    },
    {
      key: 'create_time',
      title: $t('page.manage.user.createTime'),
      align: 'center',
      width: 180
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: row => {
        return h('div', { class: 'flex-center gap-8px' }, [
          h(
            NButton,
            {
              type: 'primary',
              ghost: true,
              size: 'small',
              onClick: () => edit(row.id)
            },
            { default: () => $t('common.edit') }
          ),
          h(
            NPopconfirm,
            {
              onPositiveClick: () => handleDelete(row.id)
            },
            {
              default: () => $t('common.confirmDelete'),
              trigger: () =>
                h(
                  NButton,
                  {
                    type: 'error',
                    ghost: true,
                    size: 'small'
                  },
                  { default: () => $t('common.delete') }
                )
            }
          )
        ]);
      }
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  try {
    await batchDeleteUsers(checkedRowKeys.value);
    onBatchDeleted();
  } catch (error) {
    console.error('Failed to batch delete users:', error);
  }
}

async function handleDelete(id) {
  try {
    await deleteUser(id);
    onDeleted();
  } catch (error) {
    console.error('Failed to delete user:', error);
  }
}

function edit(id) {
  handleEdit(id);
}

async function handleSubmit(formData, isEdit = false) {
  try {
    if (isEdit && editingData.value?.id) {
      const originalData = editingData.value;
      const updatedData = {};

      if (formData.nickname !== originalData.nickname) {
        updatedData.nickname = formData.nickname;
      }
      if (formData.mobile !== originalData.mobile) {
        updatedData.mobile = formData.mobile;
      }
      if (formData.points !== originalData.points) {
        updatedData.points = formData.points;
      }
      if (formData.vip_time !== originalData.vip_time) {
        const date = new Date(formData.vip_time);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        updatedData.vip_time = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }
      if (formData.password) {
        updatedData.password = formData.password;
      }

      const res = await updateUser(editingData.value.id, updatedData);
      if (res.response.data.code === 200) {
        window.$message?.success($t('common.updateSuccess'));
      } else {
        window.$message?.error($t('common.updateFailed'));
      }
    }
    getDataByPage();
  } catch (error) {
    console.error('Failed to submit user data:', error);
  }
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <UserSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :title="$t('page.manage.user.title')" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          :show-add="false"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <UserOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="handleSubmit"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
