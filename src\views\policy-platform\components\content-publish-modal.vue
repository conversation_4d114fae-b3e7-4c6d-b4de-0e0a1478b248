<script setup>
import { computed, onBeforeUnmount, ref, shallowRef, watch } from 'vue';
import { NButton, NForm, NFormItem, NInput, NModal, NSelect, NUpload, useMessage } from 'naive-ui';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import '@wangeditor/editor/dist/css/style.css';
import { createPolicyContent } from '@/service/api/policy';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

defineOptions({
  name: 'ContentPublishModal'
});

const editorRef = shallowRef();
const html = ref('');
const toolbarConfig = {};
const editorConfig = { placeholder: '请输入内容...' };
const mode = ref('default');
const showModal = ref(false);
const previewImageUrl = ref('');

const activePreviewType = ref('cover');

const onCreated = editor => {
  editorRef.value = editor;
};

onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor) {
    editor.destroy();
  }
});

const model = ref({
  parent_id: null,
  title: '',
  content: ''
});

watch(
  () => html.value,
  newValue => {
    model.value.content = newValue;
  }
);

const props = defineProps({
  activeTab: {
    type: String,
    default: 'platform'
  },
  firstLevelMenus: {
    type: Array,
    default: () => []
  },
  secondLevelMenusByMenu: {
    type: Object,
    default: () => ({})
  },
  selectedFirstLevelMenu: {
    type: [String, null],
    default: null
  },
  selectedSecondLevelMenu: {
    type: [Number, null],
    default: null
  },
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['refresh', 'select-menu', 'update:visible']);

const message = useMessage();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const loading = ref(false);
const currentFirstLevelMenu = ref('');
const availableSecondLevelMenus = ref([]);

const coverFile = ref(null);
const fileList = ref([]);
const mediaFiles = ref([]);
const mediaFileList = ref([]);

watch(
  () => currentFirstLevelMenu.value,
  newMenu => {
    if (newMenu && props.secondLevelMenusByMenu[newMenu]) {
      availableSecondLevelMenus.value = props.secondLevelMenusByMenu[newMenu].map(item => ({
        label: item.name,
        value: item.id
      }));
    } else {
      availableSecondLevelMenus.value = [];
    }
    model.value.parent_id = null;
  }
);

const rules = {
  parent_id: defaultRequiredRule,
  title: defaultRequiredRule,
  content: defaultRequiredRule
};

const title = computed(() => {
  return props.activeTab === 'platform' ? '发布平台政策内容' : '发布创业政策内容';
});

function resetForm() {
  currentFirstLevelMenu.value = '';
  availableSecondLevelMenus.value = [];
  model.value = {
    parent_id: null,
    title: '',
    content: ''
  };
  html.value = '';
  coverFile.value = null;
  fileList.value = [];
  mediaFiles.value = [];
  mediaFileList.value = [];
  restoreValidation();
}

async function handleSubmit() {
  try {
    await validate();
    loading.value = true;

    const formData = new FormData();
    formData.append('parent_id', String(model.value.parent_id));
    formData.append('title', model.value.title);
    formData.append('content', model.value.content);

    if (coverFile.value) {
      formData.append('cover', coverFile.value);
    }

    mediaFiles.value.forEach(file => {
      const actualFile = file.file || file;
      formData.append('medias[]', actualFile);
    });

    const response = await createPolicyContent(formData);

    if (response.response.data.code === 200) {
      message.success(response.response.data.message || '内容发布成功');

      // 发送选择的菜单信息
      if (currentFirstLevelMenu.value && model.value.parent_id) {
        emit('select-menu', currentFirstLevelMenu.value, model.value.parent_id);
      }

      emit('update:visible', false);
      emit('refresh');
    } else {
      message.error(response.response.data.message || '发布失败');
    }
  } catch {
    message.error('提交表单时发生错误');
  } finally {
    loading.value = false;
  }
}

function closeModal() {
  emit('update:visible', false);
  resetForm();
}

function handlePreview(file) {
  if (file && file.url) {
    previewImageUrl.value = file.url;
    activePreviewType.value = file.source === 'media' ? 'media' : 'cover';
    showModal.value = true;
  }
}

function handleCoverUpload(options) {
  if (options && options.file) {
    const actualFile = options.file.file || options.file;
    coverFile.value = actualFile;

    const reader = new FileReader();
    reader.onload = e => {
      // Ensure url is a string
      const url = typeof e.target.result === 'string' ? e.target.result : '';

      fileList.value = [
        {
          id: Date.now().toString(),
          name: actualFile.name,
          status: 'finished',
          url,
          file: actualFile,
          source: 'cover'
        }
      ];
    };
    reader.readAsDataURL(actualFile);
  }
}

function handleMediaUpload(options) {
  if (options && options.fileList && options.fileList.length > 0) {
    const newFiles = options.fileList.map(fileInfo => {
      return fileInfo.file || fileInfo;
    });

    const newFile = newFiles[newFiles.length - 1];
    mediaFiles.value = [...mediaFiles.value, newFile];

    if (newFile.type && newFile.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = e => {
        // Ensure url is a string
        const url = typeof e.target.result === 'string' ? e.target.result : '';
        mediaFileList.value.push({
          id: `${Date.now().toString()}_${mediaFileList.value.length}`,
          name: newFile.name,
          status: 'finished',
          url,
          file: newFile,
          source: 'media'
        });
      };
      reader.readAsDataURL(newFile);
    } else if (newFile.type && newFile.type.startsWith('video/')) {
      // 对于视频文件，直接使用URL.createObjectURL创建临时URL
      const url = URL.createObjectURL(newFile);
      mediaFileList.value.push({
        id: `${Date.now().toString()}_${mediaFileList.value.length}`,
        name: newFile.name,
        status: 'finished',
        url,
        file: newFile,
        source: 'media'
      });
    }
  }
}

function removeCoverImage() {
  coverFile.value = null;
  fileList.value = [];
}

watch(
  () => props.visible,
  newVisible => {
    if (newVisible) {
      // 如果打开模态框，并且有选择的菜单，则自动填充
      if (props.selectedFirstLevelMenu) {
        currentFirstLevelMenu.value = props.selectedFirstLevelMenu;

        // 设置二级菜单选项
        if (props.secondLevelMenusByMenu[props.selectedFirstLevelMenu]) {
          availableSecondLevelMenus.value = props.secondLevelMenusByMenu[props.selectedFirstLevelMenu].map(item => ({
            label: item.name,
            value: item.id
          }));

          // 如果有选择的二级菜单，则自动填充
          if (props.selectedSecondLevelMenu) {
            model.value.parent_id = props.selectedSecondLevelMenu;
          }
        }
      } else {
        resetForm();
      }
    }
  }
);
</script>

<template>
  <NModal
    :show="props.visible"
    preset="card"
    :title="title"
    :bordered="false"
    style="width: 70vw"
    size="medium"
    :segmented="{
      content: 'soft',
      footer: 'soft'
    }"
    :mask-closable="false"
    @update:show="value => emit('update:visible', value)"
  >
    <div class="modal-content">
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" label-width="100px">
        <NFormItem label="选择菜单">
          <NSelect
            v-model:value="currentFirstLevelMenu"
            :options="firstLevelMenus.map(menu => ({ label: menu, value: menu }))"
            placeholder="请选择一级菜单"
            clearable
          />
        </NFormItem>

        <NFormItem label="选择子菜单" path="parent_id">
          <NSelect
            v-model:value="model.parent_id"
            :options="availableSecondLevelMenus"
            placeholder="请选择二级菜单"
            clearable
            :disabled="!currentFirstLevelMenu"
          />
        </NFormItem>

        <NFormItem label="标题" path="title">
          <NInput v-model:value="model.title" placeholder="请输入内容标题" />
        </NFormItem>

        <NFormItem label="内容" path="content">
          <div class="editor-container">
            <Toolbar
              style="border-bottom: 1px solid #ccc"
              :editor="editorRef"
              :default-config="toolbarConfig"
              :mode="mode"
            />
            <Editor
              v-model="html"
              style="height: 320px; overflow-y: hidden"
              :default-config="editorConfig"
              :mode="mode"
              @on-created="onCreated"
            />
          </div>
        </NFormItem>

        <NFormItem label="封面图片">
          <div class="flex flex-col gap-2">
            <template v-if="fileList.length === 0">
              <NUpload
                accept="image/*"
                list-type="image-card"
                :default-upload="false"
                :file-list="fileList"
                @change="handleCoverUpload"
                @preview="handlePreview"
              />
            </template>
            <template v-else>
              <div class="flex items-center gap-4">
                <div class="image-preview" @click="handlePreview(fileList[0])">
                  <img
                    :src="fileList[0].url"
                    alt="封面图片"
                    style="width: 104px; height: 104px; object-fit: cover; cursor: pointer"
                  />
                </div>
                <NButton size="small" type="error" @click="removeCoverImage">更换封面</NButton>
              </div>
            </template>
          </div>
        </NFormItem>

        <NFormItem label="媒体文件">
          <NUpload
            accept="image/*,video/*"
            multiple
            list-type="image-card"
            :default-upload="false"
            :file-list="mediaFileList"
            @change="handleMediaUpload"
            @preview="handlePreview"
          />
        </NFormItem>
      </NForm>
    </div>

    <template #footer>
      <div class="flex justify-end gap-12px">
        <NButton @click="closeModal">取消</NButton>
        <NButton type="primary" :loading="loading" @click="handleSubmit">发布</NButton>
      </div>
    </template>
  </NModal>

  <NModal
    v-model:show="showModal"
    preset="card"
    style="width: 600px"
    :title="activePreviewType === 'media' ? '预览媒体文件' : '预览封面图片'"
  >
    <img :src="previewImageUrl" style="width: 100%" />
  </NModal>
</template>

<style scoped>
.modal-content {
  max-height: calc(70vh - 120px);
  overflow-y: auto;
  padding-right: 8px;
}

.editor-container {
  border: 1px solid #ccc;
  z-index: 100;
}
</style>
