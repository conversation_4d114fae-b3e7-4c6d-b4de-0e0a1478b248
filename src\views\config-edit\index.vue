<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { NButton, NCard, NDivider, NForm, NFormItem, NInput, useMessage } from 'naive-ui';
import { fetchConfig, updateConfig } from '@/service/api/config';
import { useFormRules } from '@/hooks/common/form';

defineOptions({ name: 'ConfigEdit' });

const message = useMessage();
const { defaultRequiredRule } = useFormRules();

// 为每个分组创建独立的表单引用
const formRefs = reactive({});

// 设置表单引用
const setFormRef = (groupName, el) => {
  if (el) {
    formRefs[groupName] = el;
  }
};

// 获取表单引用
const getFormRef = groupName => {
  return formRefs[groupName];
};

// 配置字段的中文映射和分组
const configGroups = {
  平台配置: {
    kling_apikey: '可灵APIKey',
    vidu_apikey: 'ViDU APIKey',
    siliconflow_apikey: '硅基APIKey',
    voice2text: '转录消耗积分',
    title_model_apikey: '智谱APIKey(标题生成)'
  },
  'AI员工配置(系统)': {
    aiteam_baseurl: 'API地址',
    aiteam_apikey: 'APIKey',
    aiteam_points: '积分消耗',
    aitem_model: '默认模型',
    digital_human_model: '数字人模型',
    storyboard_model: '分镜模型(对话)',
    storyboard_image_model: '分镜模型(图片生成)',
    storyboard_video_model: '分镜模型(视频生成)',
    teamwork_model: '团队协作模型'
  },
  积分消耗配置: {
    text2image: '文生图像',
    text2video: '文生视频',
    image2video: '图生视频',
    reference2video: '参考生视频',
    aiteam: 'AI团队',
    digital_points: '数字人积分',
    storyboard_points: '分镜积分',
    storyboard_image_points: '分镜(图片)积分',
    storyboard_video_points: '分镜(视频)积分',
    teamwork_points: '团队协作积分'
  }
};

// 原始数据和表单数据
const originalData = ref({});
const formData = reactive({});
const loading = ref(false);
const groupLoading = reactive({});

// 为每个分组生成独立的验证规则
const getGroupRules = groupName => {
  const ruleObj = {};
  const groupKeys = Object.keys(configGroups[groupName]);

  groupKeys.forEach(key => {
    if (key in formData && (key.includes('apikey') || key.includes('baseurl'))) {
      ruleObj[key] = defaultRequiredRule;
    }
  });

  return ruleObj;
};

// 获取配置数据
async function getConfigData() {
  loading.value = true;
  try {
    const { data, error } = await fetchConfig();
    if (error) {
      message.error('获取配置数据失败');
      return;
    }

    // 保存原始数据
    originalData.value = { ...data };

    // 初始化表单数据
    Object.keys(data).forEach(key => {
      formData[key] = data[key];
    });
  } catch {
    message.error('获取配置数据失败');
  } finally {
    loading.value = false;
  }
}

// 检查数据是否有变化
function getChangedData() {
  const changedData = {};
  Object.keys(formData).forEach(key => {
    if (formData[key] !== originalData.value[key]) {
      changedData[key] = formData[key];
    }
  });
  return changedData;
}

// 检查分组是否有数据
function hasGroupData(groupName) {
  const groupKeys = Object.keys(configGroups[groupName]);
  return groupKeys.some(key => key in formData);
}

// 保存配置 - 只验证当前分组
async function saveConfig(groupName) {
  try {
    // 只验证当前分组的表单
    const currentFormRef = getFormRef(groupName);
    if (currentFormRef) {
      await currentFormRef.validate();
    }

    const changedData = getChangedData();

    const groupKeys = Object.keys(configGroups[groupName]);
    const groupChangedData = {};

    groupKeys.forEach(key => {
      if (key in changedData) {
        groupChangedData[key] = changedData[key];
      }
    });

    if (Object.keys(groupChangedData).length === 0) {
      message.info('没有数据变更');
      return;
    }

    groupLoading[groupName] = true;
    const { error } = await updateConfig(groupChangedData);

    if (error) {
      message.error('保存配置失败');
      return;
    }

    message.success('保存配置成功');

    Object.assign(originalData.value, groupChangedData);
  } catch (error) {
    console.log(error);
    message.error(`${groupName}表单验证失败`);
  } finally {
    groupLoading[groupName] = false;
  }
}

onMounted(() => {
  getConfigData();
});
</script>

<template>
  <div class="config-edit-container">
    <NCard :bordered="false" size="small" class="flex flex-col items-center justify-center gap-4 card-wrapper">
      <div v-if="loading && Object.keys(formData).length === 0" class="py-8 text-center">加载中...</div>
      <div v-else class="w-full">
        <!-- 为每个配置分组创建独立的表单 -->
        <template v-for="(groupConfig, groupName) in configGroups" :key="groupName">
          <template v-if="hasGroupData(groupName)">
            <div class="mb-8">
              <NDivider>{{ groupName }}</NDivider>
              <NForm
                :ref="el => setFormRef(groupName, el)"
                :model="formData"
                :rules="getGroupRules(groupName)"
                label-placement="left"
                label-width="200"
                class="mx-auto w-700px"
              >
                <!-- 动态渲染配置项 -->
                <template v-for="(fieldLabel, fieldKey) in groupConfig" :key="fieldKey">
                  <NFormItem v-if="fieldKey in formData" :label="fieldLabel" :path="fieldKey">
                    <NInput
                      v-if="!fieldKey.includes('apikey') && !fieldKey.includes('password')"
                      v-model:value="formData[fieldKey]"
                      :placeholder="`请输入${fieldLabel}`"
                    />
                    <NInput
                      v-else
                      v-model:value="formData[fieldKey]"
                      :placeholder="`请输入${fieldLabel}`"
                      type="password"
                      show-password-on="click"
                    />
                  </NFormItem>
                </template>
                <NFormItem class="w-full flex items-center justify-end">
                  <NButton type="primary" :loading="groupLoading[groupName]" @click="saveConfig(groupName)">
                    保存{{ groupName }}
                  </NButton>
                </NFormItem>
              </NForm>
            </div>
          </template>
        </template>
      </div>
    </NCard>
  </div>
</template>

<style scoped>
.config-edit-container {
  padding: 16px;
}
</style>
