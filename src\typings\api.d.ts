/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /** common search params of table */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /**
     * enable status
     *
     * - "1": enabled
     * - "2": disabled
     */
    type EnableStatus = '1' | '2';

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createBy: string;
      /** record create time */
      createTime: string;
      /** record updater */
      updateBy: string;
      /** record update time */
      updateTime: string;
      /** record status */
      status: EnableStatus | null;
    } & T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginToken {
      accessToken: string;
      refreshToken: string;
      avatar: string;
      username: string;
      nickname: string;
      expires: string;
    }

    interface UserInfo {
      userId: string;
      userName: string;
      roles: string[];
      buttons: string[];
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }

  /** Common response structure */
  interface Response<T = any> {
    code: number;
    data: T;
    message: string;
  }

  /** Pagination result */
  interface PageResult<T = any> {
    list: T[];
    total: number;
    page: number;
    pageSize: number;
  }

  /** User item */
  interface UserItem {
    id: number;
    member_id: string;
    nickname: string;
    mobile: string;
    avatar: string | null;
    materials: string | null;
    vip_level: number;
    vip_time: string | null;
    points: number;
    create_time: string;
    update_time: string;
  }

  /** User search params */
  interface UserSearchParams {
    nickname?: string;
    mobile?: string;
    page?: number;
    pageSize?: number;
  }
}

declare namespace Api.CreationRecord {
  interface RecordItem {
    id: number;
    member_id: number;
    task_id: string;
    type: string;
    state: string;
    prompt: string;
    note: string;
    points_used: number;
    creations: {
      id: string;
      url: string;
      cover_url: string;
      watermarked_url: string;
    }[];
    created_at: string;
    updated_at: string;
  }

  interface Pagination {
    total: number;
    currentPage: number;
    pageSize: number;
    totalPages: number;
  }

  interface ListResponseData {
    list: RecordItem[];
    pagination: Pagination;
  }

  interface ListResponse extends Api.Response<ListResponseData> {}
}

declare namespace Api.SystemManage {
  // Assuming UserSearchParams might be used generally or you might have other SystemManage types
  type UserSearchParams = Api.UserSearchParams;
}

declare namespace Api.Model {
  interface ModelItem {
    id: number;
    avatar: string;
    member_id: number;
    name: string;
    model_id: string;
    points: number;
    prompt: string;
    temperature: number;
    baseurl?: string;
    apikey?: string;
  }
}
