<script setup lang="ts">
import { watch } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';

defineOptions({
  name: 'LineChart'
});

const props = defineProps({
  dailyVisits: {
    type: Object,
    default: () => ({ dates: [], values: [] })
  }
});

const appStore = useAppStore();

const { domRef, updateOptions } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    }
  },
  legend: {
    data: [$t('page.home.dailyVisits')]
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: []
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      color: '#8e9dff',
      name: $t('page.home.dailyVisits'),
      type: 'line',
      smooth: true,
      stack: 'Total',
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0.25,
              color: '#8e9dff'
            },
            {
              offset: 1,
              color: '#fff'
            }
          ]
        }
      },
      emphasis: {
        focus: 'series'
      },
      data: []
    }
  ]
}));

function updateChartData() {
  if (props.dailyVisits && props.dailyVisits.dates && props.dailyVisits.dates.length > 0) {
    updateOptions(opts => {
      opts.xAxis.data = props.dailyVisits.dates;
      opts.series[0].data = props.dailyVisits.values;
      return opts;
    });
  } else {
    // Clear chart data if props are empty
    updateOptions(opts => {
      opts.xAxis.data = [];
      opts.series[0].data = [];
      return opts;
    });
  }
}

function updateLocale() {
  updateOptions((opts, factory) => {
    const originOpts = factory();
    opts.legend.data = originOpts.legend.data;
    opts.series[0].name = originOpts.series[0].name;
    return opts;
  });
}

async function init() {
  updateChartData();
}

watch(
  () => props.dailyVisits,
  () => {
    updateChartData();
  },
  { deep: true }
);

watch(
  () => appStore.locale,
  () => {
    updateLocale();
  }
);

// init
init();
</script>

<template>
  <NCard :bordered="false" class="card-wrapper">
    <div ref="domRef" class="h-[18.75vw] overflow-hidden"></div>
  </NCard>
</template>

<style scoped></style>
