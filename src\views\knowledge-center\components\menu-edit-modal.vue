<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ard, NDivider, NInput, NModal, NSpace, useMessage } from 'naive-ui';
import { updateCourse } from '@/service/api/course';

defineOptions({
  name: 'MenuEditModal'
});

interface Props {
  rowData: {
    id: string;
    parentMenu: string;
    childIds: number[];
    originalItem: {
      level_one: string;
      children: {
        id: number;
        level_two: string;
        created_at: string;
        updated_at: string;
      }[];
    };
  } | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'refresh'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const message = useMessage();

const firstLevelMenu = ref('');
const firstLevelLoading = ref(false);

const secondLevelMenus = ref<{ id: number; original: string; current: string; changed: boolean }[]>([]);

watch(
  () => props.rowData,
  newData => {
    if (newData) {
      firstLevelMenu.value = newData.parentMenu;

      secondLevelMenus.value = newData.originalItem.children.map(child => ({
        parent_id: child.id,
        original: child.level_two,
        current: child.level_two,
        changed: false
      }));
    }
  },
  { immediate: true }
);

const firstLevelChanged = computed(() => {
  if (!props.rowData) return false;
  return firstLevelMenu.value !== props.rowData.parentMenu;
});

async function updateFirstLevelMenu() {
  if (!firstLevelChanged.value || !props.rowData) {
    message.warning('菜单名称未修改');
    return;
  }

  try {
    firstLevelLoading.value = true;

    const response = await updateCourse({
      parent_id: props.rowData.id,
      level_one: firstLevelMenu.value
    });

    if (response.response.data.code === 200) {
      message.success(response.response.data.message || '一级菜单更新成功');
      if (props.rowData) {
        props.rowData.parentMenu = firstLevelMenu.value;
      }
    } else {
      message.error(response.response.data.message || '更新失败');
    }
  } catch (error) {
    message.error('更新一级菜单时发生错误');
  } finally {
    firstLevelLoading.value = false;
  }
}

function handleSecondLevelMenuChange(index: number, value: string) {
  const item = secondLevelMenus.value[index];
  item.current = value;
  item.changed = item.current !== item.original;
}

async function updateSecondLevelMenu(index: number) {
  const item = secondLevelMenus.value[index];

  if (!item.changed || !props.rowData) {
    message.warning('菜单名称未修改');
    return;
  }

  try {
    const response = await updateCourse({
      parent_id: props.rowData.id,
      level_two: item.original,
      level_two_new: item.current
    });

    if (response.response.data.code === 200) {
      message.success(response.response.data.message || '二级菜单更新成功');
      item.original = item.current;
      item.changed = false;
    } else {
      message.error(response.response.data.message || '更新失败');
      item.current = item.original;
      item.changed = false;
    }
  } catch (error) {
    message.error('更新二级菜单时发生错误');
    item.current = item.original;
    item.changed = false;
  }
}

async function updateAllChangedSecondLevelMenus() {
  if (!props.rowData) return;

  if (!secondLevelMenus.value.some(item => item.changed)) {
    message.warning('没有需要更新的菜单');
    return;
  }

  const result = { successCount: 0 };

  const updatePromises = [];

  for (const item of secondLevelMenus.value) {
    if (item.changed) {
      const promise = updateCourse({
        parent_id: props.rowData.id,
        level_two: item.original,
        level_two_new: item.current
      });

      updatePromises.push(
        promise
          .then(response => {
            if (response.response.data.code === 200) {
              result.successCount += 1;
              item.original = item.current;
            } else {
              message.error(`更新 "${item.original}" 失败: ${response.response.data.message || '未知错误'}`);
              item.current = item.original;
            }
            item.changed = false;
          })
          .catch(() => {
            message.error(`更新 "${item.original}" 失败`);
            item.current = item.original;
            item.changed = false;
          })
      );
    }
  }

  try {
    await Promise.all(updatePromises);
  } catch {
    message.error('批量更新过程中发生错误');
  }

  if (result.successCount > 0) {
    message.success(`成功更新 ${result.successCount} 个子菜单`);
    visible.value = false;
    emit('refresh');
  }
}

function closeModal() {
  visible.value = false;
}
</script>

<template>
  <NModal
    v-model:show="visible"
    preset="card"
    title="编辑菜单"
    :bordered="false"
    size="medium"
    style="width: auto"
    :segmented="{
      content: 'soft',
      footer: 'soft'
    }"
    :mask-closable="false"
  >
    <div class="modal-content">
      <NCard title="一级菜单" :bordered="false" class="mb-8px">
        <div class="flex items-center gap-12px">
          <NInput v-model:value="firstLevelMenu" placeholder="请输入一级菜单名称" />
          <NButton
            type="primary"
            :disabled="!firstLevelChanged"
            :loading="firstLevelLoading"
            @click="updateFirstLevelMenu"
          >
            修改菜单
          </NButton>
        </div>
      </NCard>

      <NCard title="二级菜单" :bordered="false">
        <div class="grid grid-cols-2 gap-16px">
          <div v-for="(item, index) in secondLevelMenus" :key="index" class="flex items-center gap-12px">
            <NInput
              v-model:value="item.current"
              placeholder="请输入二级菜单名称"
              @update:value="value => handleSecondLevelMenuChange(index, value)"
            />
            <NButton type="primary" :disabled="!item.changed" @click="updateSecondLevelMenu(index)">修改</NButton>
          </div>

          <div class="mt-8px flex justify-end">
            <NButton
              type="primary"
              :disabled="!secondLevelMenus.some(item => item.changed)"
              @click="updateAllChangedSecondLevelMenus"
            >
              保存所有修改
            </NButton>
          </div>
        </div>
      </NCard>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <NButton @click="closeModal">关闭</NButton>
      </div>
    </template>
  </NModal>
</template>

<style scoped></style>
