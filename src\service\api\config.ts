import { request } from '../request';

/** Get config data */
export function fetchConfig() {
  return request<{
    aiteam_baseurl: string;
    aiteam_apikey: string;
    aiteam_points: string;
  }>({
    url: '/api/admin/config',
    method: 'get'
  });
}

/** Update config */
export function updateConfig(data: Record<string, string>) {
  return request<{ message: string }>({
    url: '/api/admin/config/update',
    method: 'post',
    data
  });
}
