import { request } from '../request';

export interface ApiResponse {
  code: number;
  data: {
    register_time: string;
    visits: string | number;
    member_count: number;
    daily_visits: Record<string, number>;
    member_makes_total: number;
    member_makes_by_type: {
      text2video: number;
      image2video: number;
      reference2video: number;
      text2image: number;
    };
  };
}

export function fetchConstantData() {
  return request<ApiResponse>({
    url: '/api/admin/config',
    method: 'get'
  });
}
