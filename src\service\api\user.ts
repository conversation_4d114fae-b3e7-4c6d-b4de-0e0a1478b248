import { request } from '../request';

/**
 * Get user list Response Format: { code: 200, data: { users: UserItem[], pagination: { total: number, currentPage:
 * number, pageSize: number, totalPages: number } } }
 */
export function fetchUserList(params: Api.UserSearchParams) {
  return request<{
    users: Api.UserItem[];
    pagination: {
      total: number;
      currentPage: number;
      pageSize: number;
      totalPages: number;
    };
  }>({
    url: '/api/admin/users',
    method: 'get',
    params
  });
}

/** Get user detail by id */
export function fetchUserDetail(id: number) {
  return request<Api.UserItem>({
    url: `/api/admin/users/${id}`,
    method: 'get'
  });
}

/** Update user */
export function updateUser(id: number, data: Partial<Api.UserItem>) {
  return request<Api.UserItem>({
    url: `/api/admin/users/${id}`,
    method: 'put',
    data
  });
}

/** Delete user */
export function deleteUser(id: number) {
  return request({
    url: `/api/admin/users/${id}`,
    method: 'delete'
  });
}

/** Batch delete users */
export function batchDeleteUsers(ids: number[]) {
  return request({
    url: '/api/admin/users/batch',
    method: 'delete',
    data: { ids }
  });
}
