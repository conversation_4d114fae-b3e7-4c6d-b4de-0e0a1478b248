<script setup lang="ts">
import { computed, watch } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';

defineOptions({
  name: '<PERSON><PERSON><PERSON>'
});

const props = defineProps({
  makesByType: {
    type: Object,
    default: () => ({
      text2video: 0,
      image2video: 0,
      reference2video: 0,
      text2image: 0
    })
  }
});

const appStore = useAppStore();

// Computed property to transform the data for the chart
const chartData = computed(() => [
  { name: $t('page.home.textToVideo'), value: props.makesByType.text2video },
  { name: $t('page.home.imageToVideo'), value: props.makesByType.image2video },
  { name: $t('page.home.referenceToVideo'), value: props.makesByType.reference2video },
  { name: $t('page.home.textToImage'), value: props.makesByType.text2image }
]);

const { domRef, updateOptions } = useEcharts(() => ({
  tooltip: {
    trigger: 'item'
  },
  legend: {
    bottom: '1%',
    left: 'center',
    itemStyle: {
      borderWidth: 0
    }
  },
  series: [
    {
      color: ['#5da8ff', '#8e9dff', '#fedc69', '#26deca'],
      name: $t('page.home.makeCount'),
      type: 'pie',
      radius: ['45%', '75%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 1
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '12'
        }
      },
      labelLine: {
        show: false
      },
      data: chartData.value
    }
  ]
}));

function updateChartData() {
  updateOptions(opts => {
    opts.series[0].data = chartData.value;
    return opts;
  });
}

function updateLocale() {
  updateOptions((opts, factory) => {
    const originOpts = factory();

    opts.series[0].name = originOpts.series[0].name;
    opts.series[0].data = chartData.value;

    return opts;
  });
}

// Watch for changes in chartData and update the chart
watch(
  () => chartData.value,
  () => {
    updateChartData();
  },
  { deep: true }
);

watch(
  () => appStore.locale,
  () => {
    updateLocale();
  }
);
</script>

<template>
  <NCard :bordered="false" class="card-wrapper">
    <div ref="domRef" class="h-[18.75vw] overflow-hidden"></div>
  </NCard>
</template>

<style scoped></style>
