import { request } from '../request';

/**
 * Fetch creation record list
 *
 * @param params - The params for the request
 * @returns - The response from the request
 */
export function fetchCreationRecordList(params?: Api.SystemManage.UserSearchParams) {
  const processedParams = { ...params };

  if (processedParams.page !== undefined) {
    if (Array.isArray(processedParams.page)) {
      processedParams.page = Number(processedParams.page[0]) || 1;
    } else {
      processedParams.page = Number(processedParams.page) || 1;
    }
  }

  if (processedParams.pageSize !== undefined) {
    if (Array.isArray(processedParams.pageSize)) {
      processedParams.pageSize = Number(processedParams.pageSize[0]) || 10;
    } else {
      processedParams.pageSize = Number(processedParams.pageSize) || 10;
    }
  }

  return request<Api.CreationRecord.ListResponse>({
    url: '/api/admin/make/list',
    method: 'get',
    params: processedParams
  });
}
