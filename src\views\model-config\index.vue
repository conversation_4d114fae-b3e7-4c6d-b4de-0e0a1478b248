<script setup lang="ts">
import { computed, h, onMounted, ref } from 'vue';
import {
  NButton,
  NCard,
  NDataTable,
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NPagination,
  NPopconfirm,
  NSpace,
  useMessage
} from 'naive-ui';
import type { DataTableColumns } from 'naive-ui';
import {
  addModelConfig,
  createGlobalModelConfig,
  deleteModelConfig,
  fetchModelList,
  updateModelConfig
} from '@/service/api';
import ModelEditDrawer from './components/model-edit-drawer.vue';

defineOptions({ name: 'ModelConfig' });

const message = useMessage();
const loading = ref(false);
const tableData = ref<Api.Model.ModelItem[]>([]);
const globalConfig = ref<string>('');

// Pagination state
const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  pageCount: 1
});

// Global config form
const showGlobalConfigCard = ref(false);
const globalConfigForm = ref({
  aiteam_baseurl: '',
  aiteam_apikey: '',
  aiteam_points: 0
});

// Drawer state
const drawerVisible = ref(false);
const editingModel = ref<Partial<Api.Model.ModelItem> | null>(null);
const isEditingGlobal = ref(false);

// Fetch data
async function fetchData() {
  loading.value = true;
  try {
    const data = await fetchModelList({
      page: pagination.value.page,
      pageSize: pagination.value.pageSize
    });
    tableData.value = data.data.models.list; // 使用正确的字段名
    globalConfig.value = data.data.global_config;

    // 更新分页信息
    if (data.data.models.pagination) {
      pagination.value.itemCount = data.data.models.pagination.total;
      pagination.value.page = data.data.models.pagination.currentPage;
      pagination.value.pageSize = data.data.models.pagination.pageSize;
      pagination.value.pageCount = data.data.models.pagination.totalPages;
    }

    // 检查全局配置状态
    if (typeof globalConfig.value === 'string' && globalConfig.value === '暂无配置全局模型') {
      showGlobalConfigCard.value = true;
    } else {
      showGlobalConfigCard.value = false;

      // 如果全局配置是对象，可以直接使用
      if (typeof globalConfig.value === 'object' && globalConfig.value !== null) {
        // 可以在这里处理全局配置对象
      }
    }
  } catch (_error) {
    // 错误已被捕获，显示错误消息
    message.error('获取模型列表失败');
  } finally {
    loading.value = false;
  }
}

// Submit global config
async function submitGlobalConfig() {
  try {
    globalConfigForm.value.aiteam_points = globalConfigForm.value.aiteam_points.toString();
    const response = await createGlobalModelConfig(globalConfigForm.value);
    message.success(response.message || '全局配置创建成功');
    showGlobalConfigCard.value = false;
    fetchData();
  } catch (_error) {
    // 错误已被捕获，显示错误消息
    message.error('创建全局配置失败，请重试');
  }
}

// Handle add model
function handleAddModel() {
  // 获取全局配置的baseurl和apikey
  let baseurl = '';
  let apikey = '';

  // 如果全局配置不是字符串，而是对象，则尝试获取其中的值
  if (typeof globalConfig.value === 'object' && globalConfig.value !== null) {
    baseurl = globalConfig.value.aiteam_baseurl || '';
    apikey = globalConfig.value.aiteam_apikey || '';
  }

  editingModel.value = {
    member_id: 0,
    name: '',
    model_id: '',
    points: 0,
    prompt: '',
    temperature: 0.7,
    baseurl,
    apikey
  };
  isEditingGlobal.value = false;
  drawerVisible.value = true;
}

// Handle edit model
function handleEditModel(row: Api.Model.ModelItem) {
  editingModel.value = { ...row };
  isEditingGlobal.value = row.member_id === 0;
  drawerVisible.value = true;
}

// Handle delete model
async function handleDeleteModel(id: number) {
  try {
    const response = await deleteModelConfig(id);
    message.success(response.message || '删除成功');
    fetchData();
  } catch (_error) {
    // 错误已被捕获，显示错误消息
    message.error('删除模型失败，请重试');
  }
}

// Handle drawer close
function handleDrawerClose() {
  drawerVisible.value = false;
  editingModel.value = null;
}

// Handle model save
async function handleModelSave(model: FormData) {
  try {
    // 检查FormData中是否包含id字段来判断是新增还是更新
    const modelId = model.get('id');

    if (modelId) {
      // Update existing model
      const response = await updateModelConfig(model);
      message.success(response.message || '更新成功');
    } else {
      // Add new model
      const response = await addModelConfig(model);
      message.success(response.message || '添加成功');
    }
    drawerVisible.value = false;
    fetchData();
  } catch (_error) {
    // 错误已被捕获，显示错误消息
    message.error('保存失败，请重试');
  }
}

// Handle page change
function handlePageChange(page: number) {
  pagination.value.page = page;
  fetchData();
}

// Handle page size change
function handlePageSizeChange(pageSize: number) {
  pagination.value.pageSize = pageSize;
  pagination.value.page = 1;
  fetchData();
}

// Table columns
const columns = computed<DataTableColumns<Api.Model.ModelItem>>(() => [
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: '头像',
    key: 'avatar',
    width: 80,
    render: row => {
      return h('img', {
        src: row.avatar || '/src/assets/images/avatar.jpg',
        style: 'width: 40px; height: 40px; border-radius: 50%;'
      });
    }
  },
  {
    title: '用户',
    key: 'member_id',
    width: 100,
    render: row => {
      return row.member_id === '0' ? '全局' : row.member_id;
    }
  },
  {
    title: '模型名称',
    key: 'name',
    width: 150
  },
  {
    title: '模型ID',
    key: 'model_id',
    width: 150
  },
  {
    title: '消耗积分',
    key: 'points',
    width: 100
  },
  {
    title: '系统提示词',
    key: 'prompt',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '温度控制',
    key: 'temperature',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right',
    render: row => {
      return h(
        NSpace,
        { align: 'center' },
        {
          default: () => [
            h(
              NButton,
              {
                type: 'primary',
                size: 'small',
                onClick: () => handleEditModel(row)
              },
              { default: () => '编辑' }
            ),
            h(
              NPopconfirm,
              {
                onPositiveClick: () => handleDeleteModel(row.id)
              },
              {
                default: () => '确认删除？',
                trigger: () =>
                  h(
                    NButton,
                    {
                      type: 'error',
                      size: 'small'
                    },
                    { default: () => '删除' }
                  )
              }
            )
          ]
        }
      );
    }
  }
]);

onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="model-config-container">
    <!-- Global Config Card -->
    <NCard v-if="showGlobalConfigCard" title="全局模型配置" class="mb-4">
      <NForm :model="globalConfigForm" label-placement="left" label-width="120">
        <NFormItem label="Base URL" path="aiteam_baseurl">
          <NInput v-model:value="globalConfigForm.aiteam_baseurl" placeholder="请输入Base URL" />
        </NFormItem>
        <NFormItem label="API Key" path="aiteam_apikey">
          <NInput
            v-model:value="globalConfigForm.aiteam_apikey"
            placeholder="请输入API Key"
            type="password"
            show-password-on="click"
          />
        </NFormItem>
        <NFormItem label="消耗积分" path="aiteam_points">
          <NInputNumber v-model:value="globalConfigForm.aiteam_points" placeholder="请输入消耗积分" :min="0" />
        </NFormItem>
        <NFormItem>
          <NButton type="primary" @click="submitGlobalConfig">提交配置</NButton>
        </NFormItem>
      </NForm>
    </NCard>

    <!-- Model Table -->
    <NCard v-else title="模型配置" :bordered="false" size="small">
      <template #header-extra>
        <NButton type="primary" size="small" @click="handleAddModel">
          <template #icon>
            <icon-ic-round-plus class="text-icon" />
          </template>
          添加模型
        </NButton>
      </template>
      <NDataTable
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :bordered="false"
        striped
        :single-line="false"
        remote
        size="small"
        :scroll-x="1200"
      />
      <div class="mt-4 flex justify-end">
        <NPagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :item-count="pagination.itemCount"
          :page-count="pagination.pageCount"
          show-size-picker
          :page-sizes="[10, 20, 30, 50]"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </NCard>

    <!-- Edit Drawer -->
    <ModelEditDrawer
      v-model:visible="drawerVisible"
      :model="editingModel"
      :is-global="isEditingGlobal"
      @close="handleDrawerClose"
      @save="handleModelSave"
    />
  </div>
</template>

<style scoped>
.model-config-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
