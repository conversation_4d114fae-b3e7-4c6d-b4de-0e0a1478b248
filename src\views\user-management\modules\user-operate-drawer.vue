<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { NDatePicker, NInputNumber } from 'naive-ui';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'UserOperateDrawer'
});

const props = defineProps({
  operateType: {
    type: String,
    required: true
  },
  rowData: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['submitted']);

const visible = defineModel('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles = {
    add: $t('page.manage.user.addUser'),
    edit: $t('page.manage.user.editUser')
  };
  return titles[props.operateType];
});

const model = ref(createDefaultModel());

function createDefaultModel() {
  return {
    member_id: '',
    nickname: '',
    mobile: '',
    vip_level: 0,
    vip_time: null,
    points: 0,
    password: ''
  };
}

const rules = {
  nickname: defaultRequiredRule,
  mobile: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();
  emit('submitted', model.value, props.operateType === 'edit');
  closeDrawer();
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="360">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem :label="$t('page.manage.user.memberId')" path="member_id">
          <NInput v-model:value="model.member_id" :placeholder="$t('page.manage.user.form.memberId')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.nickname')" path="nickname">
          <NInput v-model:value="model.nickname" :placeholder="$t('page.manage.user.form.nickname')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.mobile')" path="mobile">
          <NInput v-model:value="model.mobile" :placeholder="$t('page.manage.user.form.mobile')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.password')" path="password">
          <NInput v-model:value="model.password" :placeholder="$t('page.manage.user.form.password')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.vipTime')" path="vip_time">
          <NDatePicker v-model:value="model.vip_time" type="datetime" clearable />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.points')" path="points">
          <NInputNumber v-model:value="model.points" :min="0" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-col-center {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.gap-8px {
  gap: 8px;
}
.mt-8px {
  margin-top: 8px;
}
</style>
