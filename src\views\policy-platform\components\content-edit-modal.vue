<script setup>
import { computed, onBeforeUnmount, ref, shallowRef, watch } from 'vue';
import { NButton, NForm, NFormItem, NInput, NModal, NSelect, NUpload, useMessage } from 'naive-ui';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import '@wangeditor/editor/dist/css/style.css';
import { updatePolicyContent } from '@/service/api/policy';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

defineOptions({
  name: 'ContentEditModal'
});

const editorRef = shallowRef();
const html = ref('');
const toolbarConfig = {};
const editorConfig = { placeholder: '请输入内容...' };
const mode = ref('default');
const showModal = ref(false);
const previewImageUrl = ref('');

const activePreviewType = ref('cover');

const onCreated = editor => {
  editorRef.value = editor;
};

onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor) {
    editor.destroy();
  }
});

const model = ref({
  id: null,
  parent_id: null,
  title: '',
  content: ''
});

// 用于比较的原始数据
const originalData = ref({
  id: null,
  parent_id: null,
  title: '',
  content: '',
  cover: '',
  medias: []
});

watch(
  () => html.value,
  newValue => {
    model.value.content = newValue;
  }
);

const props = defineProps({
  activeTab: {
    type: String,
    default: 'platform'
  },
  firstLevelMenus: {
    type: Array,
    default: () => []
  },
  secondLevelMenusByMenu: {
    type: Object,
    default: () => ({})
  },
  rowData: {
    type: Object,
    default: () => ({})
  },
  visible: {
    type: Boolean,
    default: false
  },
  selectedContentFirstLevelMenu: {
    type: String,
    default: ''
  },
  selectedContentSecondLevelMenu: {
    type: Number,
    default: null
  }
});

const emit = defineEmits(['update:visible', 'refresh']);

const message = useMessage();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const loading = ref(false);
const selectedFirstLevelMenu = ref('');
const availableSecondLevelMenus = ref([]);

const coverFile = ref(null);
const fileList = ref([]);
const mediaFiles = ref([]);
const mediaFileList = ref([]);

watch(
  () => selectedFirstLevelMenu.value,
  newMenu => {
    if (newMenu && props.secondLevelMenusByMenu[newMenu]) {
      availableSecondLevelMenus.value = props.secondLevelMenusByMenu[newMenu].map(item => ({
        label: item.name,
        value: item.id
      }));
    } else {
      availableSecondLevelMenus.value = [];
    }
  }
);

// 监听 rowData 变化，初始化表单数据
watch(
  () => props.rowData,
  newData => {
    if (newData) {
      initializeFormData(newData);
    }
  },
  { immediate: true }
);

function initializeFormData(data) {
  if (!data) return;

  // 保存原始数据用于比较
  originalData.value = {
    ...data,
    // 确保medias始终是数组
    medias: data.medias || []
  };

  // 设置表单数据
  model.value = {
    id: data.id,
    parent_id: data.parent_id || props.selectedContentSecondLevelMenu,
    title: data.title,
    content: data.content
  };

  html.value = data.content;

  // 如果没有parent_id，使用当前选中的二级菜单ID
  if (!data.parent_id && props.selectedContentSecondLevelMenu) {
    model.value.parent_id = props.selectedContentSecondLevelMenu;
  }

  // 设置一级菜单
  let parentMenu = null;

  // 尝试从数据中找到对应的一级菜单
  if (model.value.parent_id) {
    parentMenu = props.firstLevelMenus.find(menu => {
      return props.secondLevelMenusByMenu[menu]?.some(item => item.id === model.value.parent_id);
    });
  }

  // 如果找不到，使用当前选中的一级菜单
  if (!parentMenu && props.selectedContentFirstLevelMenu) {
    parentMenu = props.selectedContentFirstLevelMenu;
  }

  if (parentMenu) {
    selectedFirstLevelMenu.value = parentMenu;

    // 设置二级菜单选项
    if (props.secondLevelMenusByMenu[parentMenu]) {
      availableSecondLevelMenus.value = props.secondLevelMenusByMenu[parentMenu].map(item => ({
        label: item.name,
        value: item.id
      }));
    }
  }

  // 设置封面图片
  if (data.cover) {
    fileList.value = [
      {
        id: Date.now().toString(),
        name: 'cover',
        status: 'finished',
        url: data.cover,
        source: 'cover'
      }
    ];
  }

  // 设置媒体文件
  if (data.medias && Array.isArray(data.medias)) {
    mediaFileList.value = data.medias.map((media, index) => {
      // 处理新的媒体文件数据结构
      let url = '';
      let filename = `media_${index}`;

      if (typeof media === 'string') {
        // 处理旧格式：直接是URL字符串
        url = media;
      } else if (media && typeof media === 'object') {
        // 处理新格式：包含path、type和filename的对象
        url = media.path || '';
        filename = media.filename || `media_${index}`;
      }

      return {
        id: `${Date.now().toString()}_${index}`,
        name: filename,
        status: 'finished',
        url,
        source: 'media'
      };
    });
  }
}

const rules = {
  parent_id: defaultRequiredRule,
  title: defaultRequiredRule,
  content: defaultRequiredRule
};

const title = computed(() => {
  return props.activeTab === 'platform' ? '编辑平台政策内容' : '编辑创业政策内容';
});

function resetForm() {
  selectedFirstLevelMenu.value = '';
  availableSecondLevelMenus.value = [];
  model.value = {
    id: null,
    parent_id: null,
    title: '',
    content: ''
  };
  html.value = '';
  coverFile.value = null;
  fileList.value = [];
  mediaFiles.value = [];
  mediaFileList.value = [];
  restoreValidation();
}

async function handleSubmit() {
  try {
    await validate();
    loading.value = true;

    const formData = new FormData();
    formData.append('parent_id', String(model.value.parent_id));
    formData.append('title', model.value.title);
    formData.append('content', model.value.content);

    if (coverFile.value) {
      formData.append('cover', coverFile.value);
    }

    if (hasMediaChanges()) {
      // 如果有媒体文件变化，添加标记
      formData.append('has_media_changes', 'true');

      // 添加新上传的媒体文件
      if (mediaFiles.value.length > 0) {
        // 只处理新上传的文件（有file属性的）
        mediaFileList.value.forEach(fileItem => {
          if (fileItem.file) {
            formData.append('medias[]', fileItem.file);
          }
        });
      }

      const keptMediaUrls = mediaFileList.value
        .filter(file => !file.file) // 只包含已有的文件（没有file属性的）
        .map(file => file.url);

      if (keptMediaUrls.length > 0) {
        formData.append('kept_media_urls', JSON.stringify(keptMediaUrls));
      }
    }

    const response = await updatePolicyContent(model.value.id, formData);

    if (response.response.data.code === 200) {
      message.success(response.response.data.message || '内容更新成功');
      emit('update:visible', false);
      emit('refresh');
    } else {
      message.error(response.response.data.message || '更新失败');
    }
  } catch {
    message.error('提交表单时发生错误');
  } finally {
    loading.value = false;
  }
}

function hasMediaChanges() {
  // 检查媒体文件是否有变化
  const currentMediaUrls = mediaFileList.value.map(file => file.url).sort();

  // 处理原始媒体数据，提取URL
  let originalMediaUrls = [];
  if (originalData.value.medias) {
    originalMediaUrls = originalData.value.medias
      .map(media => {
        if (typeof media === 'string') {
          return media;
        } else if (media && typeof media === 'object') {
          return media.path || '';
        }
        return '';
      })
      .sort();
  }

  // 如果当前媒体文件数量与原始媒体文件数量不同，则认为有变化
  if (currentMediaUrls.length !== originalMediaUrls.length) {
    return true;
  }

  // 比较URL是否相同
  return JSON.stringify(currentMediaUrls) !== JSON.stringify(originalMediaUrls);
}

function closeModal() {
  emit('update:visible', false);
  resetForm();
}

function handlePreview(file) {
  if (file && file.url) {
    previewImageUrl.value = file.url;
    activePreviewType.value = file.source === 'media' ? 'media' : 'cover';
    showModal.value = true;
  }
}

function handleCoverUpload(options) {
  if (options && options.file) {
    const actualFile = options.file.file || options.file;
    coverFile.value = actualFile;

    const reader = new FileReader();
    reader.onload = e => {
      // Ensure url is a string
      const url = typeof e.target.result === 'string' ? e.target.result : '';

      fileList.value = [
        {
          id: Date.now().toString(),
          name: actualFile.name,
          status: 'finished',
          url,
          file: actualFile,
          source: 'cover'
        }
      ];
    };
    reader.readAsDataURL(actualFile);
  }
}

function handleMediaUpload(options) {
  if (options && options.fileList && options.fileList.length > 0) {
    const newFiles = options.fileList.map(fileInfo => {
      return fileInfo.file || fileInfo;
    });

    const newFile = newFiles[newFiles.length - 1];
    mediaFiles.value = [...mediaFiles.value, newFile];

    if (newFile.type && newFile.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = e => {
        // Ensure url is a string
        const url = typeof e.target.result === 'string' ? e.target.result : '';
        mediaFileList.value.push({
          id: `${Date.now().toString()}_${mediaFileList.value.length}`,
          name: newFile.name,
          status: 'finished',
          url,
          file: newFile,
          source: 'media'
        });
      };
      reader.readAsDataURL(newFile);
    } else if (newFile.type && newFile.type.startsWith('video/')) {
      // 对于视频文件，直接使用URL.createObjectURL创建临时URL
      const url = URL.createObjectURL(newFile);
      mediaFileList.value.push({
        id: `${Date.now().toString()}_${mediaFileList.value.length}`,
        name: newFile.name,
        status: 'finished',
        url,
        file: newFile,
        source: 'media'
      });
    }
  }
}

function removeCoverImage() {
  coverFile.value = null;
  fileList.value = [];
}

function handleMediaRemove(options) {
  const { file } = options;

  // 从mediaFileList中移除文件
  const index = mediaFileList.value.findIndex(item => item.id === file.id);
  if (index !== -1) {
    mediaFileList.value.splice(index, 1);
  }

  // 如果是新上传的文件（有file属性），从mediaFiles中移除
  if (file.file) {
    const fileIndex = mediaFiles.value.findIndex(item => {
      // 比较文件对象或文件名
      if (item === file.file) return true;
      if (item.name === file.file.name) return true;
      return false;
    });

    if (fileIndex !== -1) {
      mediaFiles.value.splice(fileIndex, 1);
    }
  }
}

watch(
  () => props.visible,
  newValue => {
    if (!newValue) {
      resetForm();
    }
  }
);
</script>

<template>
  <NModal
    :show="props.visible"
    preset="card"
    :title="title"
    :bordered="false"
    style="width: 70vw"
    size="medium"
    :segmented="{
      content: 'soft',
      footer: 'soft'
    }"
    :mask-closable="false"
    @update:show="value => emit('update:visible', value)"
  >
    <div class="modal-content">
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" label-width="100px">
        <NFormItem label="一级菜单">
          <NSelect
            v-model:value="selectedFirstLevelMenu"
            :options="firstLevelMenus.map(menu => ({ label: menu, value: menu }))"
            placeholder="请选择一级菜单"
            disabled
          />
        </NFormItem>

        <NFormItem label="二级菜单" path="parent_id">
          <NSelect
            v-model:value="model.parent_id"
            :options="availableSecondLevelMenus"
            placeholder="请选择二级菜单"
            disabled
          />
        </NFormItem>

        <NFormItem label="标题" path="title">
          <NInput v-model:value="model.title" placeholder="请输入内容标题" />
        </NFormItem>

        <NFormItem label="内容" path="content">
          <div class="editor-container">
            <Toolbar
              style="border-bottom: 1px solid #ccc"
              :editor="editorRef"
              :default-config="toolbarConfig"
              :mode="mode"
            />
            <Editor
              v-model="html"
              style="height: 320px; overflow-y: hidden"
              :default-config="editorConfig"
              :mode="mode"
              @on-created="onCreated"
            />
          </div>
        </NFormItem>

        <NFormItem label="封面图片">
          <div class="flex flex-col gap-2">
            <template v-if="fileList.length === 0">
              <NUpload
                accept="image/*"
                list-type="image-card"
                :default-upload="false"
                :file-list="fileList"
                @change="handleCoverUpload"
                @preview="handlePreview"
              />
            </template>
            <template v-else>
              <div class="flex items-center gap-4">
                <div class="image-preview" @click="handlePreview(fileList[0])">
                  <img
                    :src="fileList[0].url"
                    alt="封面图片"
                    style="width: 104px; height: 104px; object-fit: cover; cursor: pointer"
                  />
                </div>
                <NButton size="small" type="error" @click="removeCoverImage">更换封面</NButton>
              </div>
            </template>
          </div>
        </NFormItem>

        <NFormItem label="媒体文件">
          <NUpload
            accept="image/*,video/*"
            multiple
            list-type="image-card"
            :default-upload="false"
            :file-list="mediaFileList"
            @change="handleMediaUpload"
            @preview="handlePreview"
            @remove="handleMediaRemove"
          />
        </NFormItem>
      </NForm>
    </div>

    <template #footer>
      <div class="flex justify-end gap-12px">
        <NButton @click="closeModal">取消</NButton>
        <NButton type="primary" :loading="loading" @click="handleSubmit">保存</NButton>
      </div>
    </template>
  </NModal>

  <NModal
    v-model:show="showModal"
    preset="card"
    style="width: 600px"
    :title="activePreviewType === 'media' ? '预览媒体文件' : '预览封面图片'"
  >
    <img :src="previewImageUrl" style="width: 100%" />
  </NModal>
</template>

<style scoped>
.modal-content {
  max-height: calc(70vh - 120px);
  overflow-y: auto;
  padding-right: 8px;
}

.editor-container {
  border: 1px solid #ccc;
  z-index: 100;
}
</style>
