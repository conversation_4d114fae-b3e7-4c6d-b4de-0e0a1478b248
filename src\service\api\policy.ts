import { request } from '../request';

/** Get policy menu list */
export function fetchPolicyList(params: { title: 'platform' | 'entre' }) {
  return request({
    url: '/api/policy/list/',
    method: 'get',
    params
  });
}

/** Delete policy menu */
export function deletePolicy(id: number) {
  return request({
    url: `/api/admin/policy/${id}`,
    method: 'delete'
  });
}

/** Update policy menu */
export function updatePolicy(data: any) {
  return request({
    url: `/api/admin/policy/update/`,
    method: 'post',
    data
  });
}

/** Create policy menu */
export function createPolicy(data: any) {
  return request({
    url: '/api/admin/policy/create/',
    method: 'post',
    data
  });
}

/** Create policy content with media files */
export function createPolicyContent(data: FormData) {
  return request({
    url: '/api/admin/policy/create/content',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  });
}

/** Get policy content list */
export function fetchPolicyContentList(params: { id: number }) {
  return request({
    url: `/api/policy/contents/?id=${params.id}`,
    method: 'get'
  });
}

/** Delete policy content */
export function deletePolicyContent(id: number) {
  return request({
    url: `/api/admin/policy/delete/content/${id}`,
    method: 'delete'
  });
}

/** Update policy content */
export function updatePolicyContent(id: number, data: FormData) {
  return request({
    url: `/api/admin/policy/update/content/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  });
}
