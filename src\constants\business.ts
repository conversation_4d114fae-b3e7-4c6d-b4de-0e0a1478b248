import { $t } from '@/locales';

/**
 * User gender options
 * - 1: Male
 * - 2: Female
 */
export const userGenderOptions = [
  { value: 1, label: 'page.manage.user.gender.male' },
  { value: 2, label: 'page.manage.user.gender.female' }
];

/**
 * Enable status options
 * - 1: Enabled
 * - 2: Disabled
 */
export const enableStatusOptions = [
  { value: 1, label: 'page.common.status.enabled' },
  { value: 2, label: 'page.common.status.disabled' }
];

/**
 * User gender record
 * - 1: Male
 * - 2: Female
 */
export const userGenderRecord = {
  1: 'page.manage.user.gender.male',
  2: 'page.manage.user.gender.female'
};

/**
 * Enable status record
 * - 1: Enabled
 * - 2: Disabled
 */
export const enableStatusRecord = {
  1: 'page.common.status.enabled',
  2: 'page.common.status.disabled'
};
