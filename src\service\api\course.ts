import { request } from '../request';

/** Get course menu list */
export function fetchCourseList(params: { title: 'free' | 'paid' }) {
  return request({
    url: '/api/course/list/',
    method: 'get',
    params
  });
}

/** Delete course menu */
export function deleteCourse(id: number) {
  return request({
    url: `/api/admin/course/${id}`,
    method: 'delete'
  });
}

/** Update course menu */
export function updateCourse(data: any) {
  return request({
    url: `/api/admin/course/update/`,
    method: 'post',
    data
  });
}

/** Create course menu */
export function createCourse(data: any) {
  return request({
    url: '/api/admin/course/create/',
    method: 'post',
    data
  });
}

/** Create course content with media files */
export function createCourseContent(data: FormData) {
  return request({
    url: '/api/admin/course/create/content',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  });
}

/** Get course content list */
export function fetchCourseContentList(params: { id: number; page?: number }) {
  return request({
    url: `/api/course/contents/`,
    method: 'get',
    params
  });
}

/** Delete course content */
export function deleteCourseContent(id: number) {
  return request({
    url: `/api/admin/course/delete/content/${id}`,
    method: 'delete'
  });
}

/** Update course content */
export function updateCourseContent(id: number, data: FormData) {
  return request({
    url: `/api/admin/course/update/content/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  });
}
