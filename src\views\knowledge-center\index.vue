<script setup lang="ts">
import { computed, h, ref, watch } from 'vue';
import {
  NButton,
  NCard,
  NDataTable,
  NModal,
  NPagination,
  NSelect,
  NSpace,
  NTabPane,
  NTabs,
  useDialog,
  useMessage
} from 'naive-ui';
import type { DataTableColumns } from 'naive-ui';
import {
  createCourse,
  deleteCourse,
  deleteCourseContent,
  fetchCourseContentList,
  fetchCourseList,
  updateCourse
} from '@/service/api/course';
import MenuDrawer from './components/menu-drawer.vue';
import MenuEditModal from './components/menu-edit-modal.vue';
import ContentPublishModal from './components/content-publish-modal.vue';
import ContentEditModal from './components/content-edit-modal.vue';

defineOptions({ name: 'KnowledgeCenter' });

interface CourseChild {
  id: number;
  level_two: string;
  created_at: string;
  updated_at: string;
}

interface CourseItem {
  level_one: string;
  children: CourseChild[];
}

interface TableRowData {
  key: string;
  id: string;
  parentMenu: string;
  subMenu: string;
  updatedAt: string;
  childIds: number[];
  originalItem: CourseItem;
}

interface ContentTableRowData {
  id: number;
  title: string;
  parentMenu: string;
  subMenu: string;
  createdAt: string;
  updatedAt: string;
}

const dialog = useDialog();
const message = useMessage();

const activeTab = ref<'free' | 'paid'>('free');
const loading = ref(false);
const tableData = ref<TableRowData[]>([]);
const drawerVisible = ref(false);
const editModalVisible = ref(false);
const currentEditRow = ref<TableRowData | null>(null);
const contentPublishModalVisible = ref(false);

// Content management
const contentTableData = ref<ContentTableRowData[]>([]);
const contentLoading = ref(false);
const selectedContentFirstLevelMenu = ref(null);
const selectedContentSecondLevelMenu = ref<number | null>(null);
const contentAvailableSecondLevelMenus = ref<Array<{ label: string; value: number }>>([]);
const mediaFiles = ref<Array<File>>([]);
const contentPagination = ref({
  page: 1,
  pageSize: 20,
  pageCount: 1,
  itemCount: 0
});

const existingMenus = computed(() => {
  return tableData.value.map(item => item.parentMenu);
});

// Create a map of second-level menus by first-level menu
const secondLevelMenusByMenu = computed(() => {
  const menuMap: Record<string, { id: number; name: string }[]> = {};

  tableData.value.forEach(item => {
    menuMap[item.parentMenu] = item.originalItem.children.map(child => ({
      id: child.id,
      name: child.level_two
    }));
  });

  return menuMap;
});

const processData = (items: CourseItem[]): TableRowData[] => {
  return items.map(item => {
    const subMenuStr = item.children.map(child => child.level_two).join(' - ');

    let latestUpdatedAt = new Date(0).toISOString();
    if (item.children && item.children.length > 0) {
      latestUpdatedAt = item.children.reduce((latest, child) => {
        const childDate = new Date(child.updated_at);
        return childDate > new Date(latest) ? child.updated_at : latest;
      }, item.children[0].updated_at);
    }

    const childIds = item.children.map(child => child.id);

    return {
      key: item.level_one,
      id: item.id,
      parentMenu: item.level_one,
      subMenu: subMenuStr,
      updatedAt: latestUpdatedAt === new Date(0).toISOString() ? 'N/A' : new Date(latestUpdatedAt).toLocaleString(),
      childIds,
      originalItem: item
    };
  });
};

const fetchData = async (title: 'free' | 'paid') => {
  loading.value = true;
  try {
    tableData.value = [];
    const response = await fetchCourseList({ title });

    if (response && response.response && response.response.data) {
      if (response.response.data.code === 200 && response.response.data.data) {
        tableData.value = processData(response.response.data.data);
      } else {
        let errorMsg = `Failed to fetch course list for ${title}.`;
        if (response.response.data.message) {
          errorMsg += ` Message: ${response.response.data.message}`;
        } else if (response.response.data.code) {
          errorMsg += ` Code: ${response.response.data.code}`;
        } else {
          errorMsg += ' Unknown error structure.';
        }
        tableData.value = [];
      }
    } else {
      tableData.value = [];
    }
  } catch (error) {
    tableData.value = [];
    console.error('Course fetch error:', error);
  } finally {
    loading.value = false;
  }
};

watch(
  activeTab,
  newTab => {
    fetchData(newTab);
    // 重置内容区域的菜单选择状态
    selectedContentFirstLevelMenu.value = null;
    selectedContentSecondLevelMenu.value = null;
    contentAvailableSecondLevelMenus.value = [];
    contentTableData.value = [];
  },
  { immediate: true }
);

const handleEdit = (row: TableRowData) => {
  currentEditRow.value = row;
  editModalVisible.value = true;
};

const handleDelete = (row: TableRowData) => {
  if (!row.childIds.length) {
    message.warning('没有可删除的子菜单项');
    return;
  }

  dialog.warning({
    title: '确认删除',
    content: `确定要删除 "${row.parentMenu}" 下的所有子菜单吗？此操作不可撤销。`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        loading.value = true;
        const response = await deleteCourse(row.id);
        if (response && response.response && response.response.data) {
          if (response.response.data.code === 200) {
            message.success(response.response.data.message || '删除成功');
            tableData.value = [];
            await fetchData(activeTab.value);
          }
        } else {
          message.error('删除失败: 无效的响应结构');
        }
      } catch (error) {
        console.error('Course delete error:', error);
      } finally {
        loading.value = false;
      }
    }
  });
};

const columns = computed(
  (): DataTableColumns<TableRowData> => [
    { title: 'ID', key: 'id', align: 'center', width: 120, ellipsis: { tooltip: true } },
    { title: '父菜单', key: 'parentMenu', align: 'left', width: 170, ellipsis: { tooltip: true } },
    { title: '子菜单', key: 'subMenu', align: 'left', minWidth: 250, ellipsis: { tooltip: true } },
    { title: '更新时间', key: 'updatedAt', align: 'center', width: 180 },
    {
      title: '操作',
      key: 'actions',
      align: 'center',
      fixed: 'right',
      width: 150,
      render(row) {
        return h(NSpace, null, {
          default: () => [
            h(
              NButton,
              { size: 'small', type: 'primary', ghost: true, onClick: () => handleEdit(row) },
              { default: () => '编辑' }
            ),
            h(
              NButton,
              { size: 'small', type: 'error', ghost: true, onClick: () => handleDelete(row) },
              { default: () => '删除' }
            )
          ]
        });
      }
    }
  ]
);

watch(
  () => selectedContentFirstLevelMenu.value,
  newMenu => {
    if (newMenu && secondLevelMenusByMenu.value[newMenu]) {
      contentAvailableSecondLevelMenus.value = secondLevelMenusByMenu.value[newMenu].map(item => ({
        label: item.name,
        value: item.id
      }));
    } else {
      contentAvailableSecondLevelMenus.value = [];
    }
    selectedContentSecondLevelMenu.value = null;
    fetchContentList();
  }
);

watch(
  () => selectedContentSecondLevelMenu.value,
  () => {
    fetchContentList();
  }
);

const fetchContentList = async () => {
  if (!selectedContentSecondLevelMenu.value) return;

  contentLoading.value = true;
  try {
    const response = await fetchCourseContentList({
      id: selectedContentSecondLevelMenu.value,
      page: contentPagination.value.page
    });
    if (response && response.response && response.response.data && response.response.data.code === 200) {
      const { list, pagination } = response.response.data.data;

      // 更新分页信息
      if (pagination) {
        contentPagination.value = {
          page: pagination.currentPage,
          pageSize: pagination.pageSize,
          pageCount: pagination.totalPages,
          itemCount: pagination.total
        };
      }

      contentTableData.value = list.map(item => ({
        id: item.id,
        title: item.title,
        content: item.content,
        cover: item.cover,
        createdAt: new Date(item.created_at).toLocaleString(),
        updatedAt: new Date(item.updated_at).toLocaleString(),
        medias: item.medias,
        view: item.view,
        points: item.points
      }));
    } else {
      contentTableData.value = [];
    }
  } catch (error) {
    contentTableData.value = [];
  } finally {
    contentLoading.value = false;
  }
};

const contentColumns = computed(
  (): DataTableColumns<any> => [
    {
      title: 'ID',
      key: 'id',
      align: 'center',
      width: 120,
      ellipsis: { tooltip: true }
    },
    {
      title: '封面',
      key: 'cover',
      align: 'center',
      width: 50,
      render(row) {
        return row.cover
          ? h('img', {
              src: row.cover,
              style: 'width: 40px; height: 40px; object-fit: cover; border-radius: 4px; cursor: pointer;',
              onClick: () => handlePreviewImage(row.cover)
            })
          : '无封面';
      }
    },
    {
      title: '标题',
      key: 'title',
      align: 'left',
      width: 200,
      ellipsis: { tooltip: true }
    },
    {
      title: '浏览量',
      key: 'view',
      align: 'left',
      width: 100,
      ellipsis: { tooltip: true }
    },
    {
      title: '内容',
      key: 'content',
      align: 'left',
      width: 300,
      ellipsis: { tooltip: true }
    },
    {
      title: '创建时间',
      key: 'createdAt',
      align: 'center',
      width: 180
    },
    {
      title: '操作',
      key: 'actions',
      align: 'center',
      fixed: 'right',
      width: 150,
      render(row) {
        return h(NSpace, null, {
          default: () => [
            h(
              NButton,
              { size: 'small', type: 'primary', ghost: true, onClick: () => handleContentEdit(row) },
              { default: () => '编辑' }
            ),
            h(
              NButton,
              { size: 'small', type: 'error', ghost: true, onClick: () => handleContentDelete(row) },
              { default: () => '删除' }
            )
          ]
        });
      }
    }
  ]
);

const contentEditModalVisible = ref(false);
const currentContentEditRow = ref(null);

// 图片预览相关
const previewImageVisible = ref(false);
const previewImageUrl = ref('');

// 处理图片预览
const handlePreviewImage = url => {
  if (url) {
    previewImageUrl.value = url;
    previewImageVisible.value = true;
  }
};

const handleContentEdit = row => {
  currentContentEditRow.value = row;
  contentEditModalVisible.value = true;
};

const handleContentDelete = (row: any) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除标题为 "${row.title}" 的课程内容吗？此操作不可撤销。`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        contentLoading.value = true;
        const response = await deleteCourseContent(row.id);
        if (response && response.response && response.response.data) {
          if (response.response.data.code === 200) {
            message.success(response.response.data.message || '删除成功');
            await fetchContentList();
          } else {
            message.error(response.response.data.message || '删除失败');
          }
        } else {
          message.error('删除失败: 无效的响应结构');
        }
      } catch {
        message.error('删除过程中发生错误');
      } finally {
        contentLoading.value = false;
      }
    }
  });
};

// 处理分页变化
const handlePageChange = (page: number) => {
  contentPagination.value.page = page;
  fetchContentList();
};

// 处理内容发布成功后的刷新
const handleContentPublish = () => {
  // 刷新菜单数据
  fetchData(activeTab.value);

  // 如果已经选择了二级菜单，刷新内容列表
  if (selectedContentSecondLevelMenu.value) {
    fetchContentList();
  }
};

// 处理选择菜单事件
const handleSelectMenu = (firstLevelMenu: string, secondLevelMenuId: number) => {
  // 设置选中的一级菜单
  selectedContentFirstLevelMenu.value = firstLevelMenu;

  // 设置二级菜单选项
  if (secondLevelMenusByMenu.value[firstLevelMenu]) {
    contentAvailableSecondLevelMenus.value = secondLevelMenusByMenu.value[firstLevelMenu].map(item => ({
      label: item.name,
      value: item.id
    }));
  }

  // 设置选中的二级菜单
  selectedContentSecondLevelMenu.value = secondLevelMenuId;
};
</script>

<template>
  <div class="knowledge-center-container">
    <div class="knowledge-center-page">
      <NCard :bordered="false" size="small" class="content-card">
        <NTabs v-model:value="activeTab" type="line" animated class="flex-tabs">
          <NTabPane name="free" tab="免费课程" class="flex-tab-pane">
            <NCard title="菜单配置" :bordered="false" size="small" class="table-card mb-16px">
              <template #header-extra>
                <NButton type="primary" size="small" @click="drawerVisible = true">添加菜单</NButton>
              </template>
              <NDataTable
                :columns="columns"
                :data="tableData"
                :loading="loading"
                :bordered="false"
                striped
                :single-line="false"
                remote
                size="small"
                :scroll-x="900"
                :max-height="300"
                virtual-scroll
                :row-key="r => r.key"
                class="h-full"
              />
            </NCard>

            <NCard title="内容管理" :bordered="false" size="small" class="table-card">
              <template #header-extra>
                <NButton type="primary" size="small" @click="contentPublishModalVisible = true">发布内容</NButton>
              </template>
              <div class="mb-4 flex gap-4">
                <NSelect
                  v-model:value="selectedContentFirstLevelMenu"
                  :options="existingMenus.map(menu => ({ label: menu, value: menu }))"
                  placeholder="请选择一级菜单"
                  clearable
                  class="w-200px"
                />
                <NSelect
                  v-model:value="selectedContentSecondLevelMenu"
                  :options="contentAvailableSecondLevelMenus"
                  placeholder="请选择二级菜单"
                  clearable
                  :disabled="!selectedContentFirstLevelMenu"
                  class="w-200px"
                />
              </div>
              <NDataTable
                :columns="contentColumns"
                :data="contentTableData"
                :loading="contentLoading"
                :bordered="false"
                striped
                :single-line="false"
                remote
                size="small"
                :scroll-x="1000"
                :max-height="300"
                virtual-scroll
                :row-key="r => r.id"
                class="h-full"
              />
              <div class="mt-4 flex justify-end">
                <NPagination
                  v-model:page="contentPagination.page"
                  :page-size="contentPagination.pageSize"
                  :item-count="contentPagination.itemCount"
                  show-size-picker
                  :page-sizes="[10, 20, 30, 40]"
                  show-quick-jumper
                  @update:page="handlePageChange"
                />
              </div>
            </NCard>
          </NTabPane>
          <NTabPane name="paid" tab="付费课程" class="flex-tab-pane">
            <NCard title="菜单配置" :bordered="false" size="small" class="table-card mb-16px">
              <template #header-extra>
                <NButton type="primary" size="small" @click="drawerVisible = true">添加菜单</NButton>
              </template>
              <NDataTable
                :columns="columns"
                :data="tableData"
                :loading="loading"
                :bordered="false"
                striped
                :single-line="false"
                remote
                size="small"
                :scroll-x="900"
                :max-height="300"
                virtual-scroll
                :row-key="r => r.key"
                class="h-full"
              />
            </NCard>

            <NCard title="内容管理" :bordered="false" size="small" class="table-card">
              <template #header-extra>
                <NButton type="primary" size="small" @click="contentPublishModalVisible = true">发布内容</NButton>
              </template>
              <div class="mb-4 flex gap-4">
                <NSelect
                  v-model:value="selectedContentFirstLevelMenu"
                  :options="existingMenus.map(menu => ({ label: menu, value: menu }))"
                  placeholder="请选择一级菜单"
                  clearable
                  class="w-200px"
                />
                <NSelect
                  v-model:value="selectedContentSecondLevelMenu"
                  :options="contentAvailableSecondLevelMenus"
                  placeholder="请选择二级菜单"
                  clearable
                  :disabled="!selectedContentFirstLevelMenu"
                  class="w-200px"
                />
              </div>
              <NDataTable
                :columns="contentColumns"
                :data="contentTableData"
                :loading="contentLoading"
                :bordered="false"
                striped
                :single-line="false"
                remote
                size="small"
                :scroll-x="1000"
                :max-height="300"
                virtual-scroll
                :row-key="r => r.id"
                class="h-full"
              />
              <div class="mt-4 flex justify-end">
                <NPagination
                  v-model:page="contentPagination.page"
                  :page-size="contentPagination.pageSize"
                  :item-count="contentPagination.itemCount"
                  show-size-picker
                  :page-sizes="[10, 20, 30, 40]"
                  show-quick-jumper
                  @update:page="handlePageChange"
                />
              </div>
            </NCard>
          </NTabPane>
        </NTabs>
      </NCard>
    </div>

    <MenuDrawer
      v-model:visible="drawerVisible"
      :active-tab="activeTab"
      :existing-menus="existingMenus"
      @refresh="fetchData(activeTab)"
    />

    <MenuEditModal v-model:visible="editModalVisible" :row-data="currentEditRow" @refresh="fetchData(activeTab)" />

    <ContentPublishModal
      v-model:visible="contentPublishModalVisible"
      :active-tab="activeTab"
      :first-level-menus="existingMenus"
      :second-level-menus-by-menu="secondLevelMenusByMenu"
      :selected-first-level-menu="selectedContentFirstLevelMenu"
      :selected-second-level-menu="selectedContentSecondLevelMenu"
      @refresh="handleContentPublish"
      @select-menu="handleSelectMenu"
    />

    <ContentEditModal
      :visible="contentEditModalVisible"
      :active-tab="activeTab"
      :first-level-menus="existingMenus"
      :second-level-menus-by-menu="secondLevelMenusByMenu"
      :row-data="currentContentEditRow"
      :selected-content-first-level-menu="selectedContentFirstLevelMenu"
      :selected-content-second-level-menu="selectedContentSecondLevelMenu"
      @update:visible="contentEditModalVisible = $event"
      @refresh="fetchContentList"
    />

    <!-- 图片预览模态框 -->
    <NModal v-model:show="previewImageVisible" preset="card" style="width: auto; max-width: 80vw">
      <template #header>
        <div class="text-lg font-bold">图片预览</div>
      </template>
      <img :src="previewImageUrl" style="max-width: 100%; max-height: 80vh" />
    </NModal>
  </div>
</template>

<style scoped></style>
