/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'config-edit',
    path: '/config-edit',
    component: 'layout.base$view.config-edit',
    meta: {
      title: '系统配置',
      icon: 'material-symbols:settings',
      order: 2,
      i18nKey: 'route.config-edit'
    }
  },
  {
    name: 'creation-record',
    path: '/creation-record',
    component: 'layout.base$view.creation-record',
    meta: {
      title: '制作记录',
      icon: 'mdi:clipboard-text-clock',
      order: 4,
      i18nKey: 'route.creation-record'
    }
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 1
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'inspiration-square',
    path: '/inspiration-square',
    component: 'layout.base$view.inspiration-square',
    meta: {
      title: '灵感广场',
      icon: 'mdi:lightbulb-on',
      order: 5,
      i18nKey: 'route.inspiration-square'
    }
  },
  {
    name: 'knowledge-center',
    path: '/knowledge-center',
    component: 'layout.base$view.knowledge-center',
    meta: {
      title: '知识中心',
      icon: 'mdi:book-open-page-variant',
      order: 7,
      i18nKey: 'route.knowledge-center'
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'model-config',
    path: '/model-config',
    component: 'layout.base$view.model-config',
    meta: {
      title: '模型配置',
      icon: 'mdi:robot-angry',
      order: 4,
      i18nKey: 'route.model-config'
    }
  },
  {
    name: 'order-record',
    path: '/order-record',
    component: 'layout.base$view.order-record',
    meta: {
      title: '订单记录',
      icon: 'mdi:clipboard-text-clock',
      order: 9,
      i18nKey: 'route.order-record'
    }
  },
  {
    name: 'policy-platform',
    path: '/policy-platform',
    component: 'layout.base$view.policy-platform',
    meta: {
      title: '政策平台',
      icon: 'mdi:gavel',
      order: 6,
      i18nKey: 'route.policy-platform'
    }
  },
  {
    name: 'recharge-package',
    path: '/recharge-package',
    component: 'layout.base$view.recharge-package',
    meta: {
      title: '充值套餐',
      icon: 'mdi:credit-card-plus',
      order: 8,
      i18nKey: 'route.recharge-package'
    }
  },
  {
    name: 'user-management',
    path: '/user-management',
    component: 'layout.base$view.user-management',
    meta: {
      title: '用户管理',
      icon: 'ph:users-bold',
      order: 3,
      i18nKey: 'route.user-management'
    }
  }
];
